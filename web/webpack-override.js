/**
 * 固定回调，在这里重写webpack的配置
 * @param {*} config webpack的配置,可在此基础上进行修改
 * @param {*} env 环境变了，development｜production
 * @return {*} 必须返回修改后的config
 */
const { name, project } = require('../editor/package.json');

let port = 3000;
const str = name + project;
for (let i of str) {
  port += i.charCodeAt();
}

module.exports = function (config, env) {
  if (env === 'development') {
    config.devServer.proxy.push({
      context: ['/api'],
      target: 'http://localhost:3000',
      pathRewrite: { '^/api': '' },
    });

    config.devServer.proxy.push({
      context: [`/${name}`],
      target: `http://localhost:${port}`,
      // target: 'http://localhost:3965',
      // pathRewrite: { '^/api': '' },
    });
  }
  config.resolve.alias.vue = 'vue/dist/vue.js';
  config.plugins[1]._options.remotes[name] =
    `${name}@/${name}/remoteEntry.js?${new Date().getTime()}`;
  return config;
};
