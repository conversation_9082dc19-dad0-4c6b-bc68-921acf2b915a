<template>
  <div class="mainBody">
    <div class="h5LoadingBox" v-if="isLoading">
      <Loading :status="true" />
    </div>
    <template v-else>
      <h3>{{ $t('onGoing') }}</h3>
      <div class="blackBox going" v-if="userInfo.membership_id">
        <div class="itemBox">
          <div class="name flexCenter flex-wrap">
            <span style="margin-right: 8px;">{{ $t('packageInfo.' + stateInfo.membership_type) }}</span>
            <div class="free-sign" :class="[stateInfo.membership_type]" v-if="stateInfo.is_free_trial_now">{{$t('freeTrial')}}</div>
          </div>
          <template v-if="stateInfo.membership_type!='unlimited'">
            <div class="barBox">
              <div
                class="process"
                :style="getProWidth(userInfo.seconds_left, userInfo.seconds_total)"
              ></div>
            </div>
            <div class="flex-middle flex-wrap">
              <span class="timeOne">{{ getTimeInfo(userInfo.seconds_left, false) }}</span>
              <span class="time">（{{ getTimeInfo(userInfo.seconds_total) }}）</span>
            </div>
          </template>
          <div class="detail">
            <div class="item" v-if="stateInfo.is_free_trial_now && (stateInfo.autorenew_status_ios || stateInfo.autorenew_status_android || stateInfo.autorenew_status_web || stateInfo.autorenew_status_stripe)">
              {{ $t('freeNext') }}
            </div>
            <div
              class="item"
              v-if="
                ((isIOS && (stateInfo.autorenew_status_ios || stateInfo.autorenew_status_stripe)) ||
                  (isAndroid && stateInfo.autorenew_status_android)) ||
                  stateInfo.autorenew_status_web
              "
            >
              {{ getPackageType() }}
              <div class="flex" v-if="!stateInfo.autorenew_status_web"><div class="btnLabel" @click="popStatus = true">{{ $t('manageSub') }}</div></div>
            </div>
            <template
              v-if="stateInfo.membership_type != 'free'"
            >
              <div class="item" v-if="stateInfo.membership_type != 'starter'">
                {{ $t('startDate') }}: {{ getUserDateStr(userInfo.start_time) }}
              </div>
              <div class="item" v-if="stateInfo.membership_type!='unlimited'">
                {{ $t('nextResetDate') }}: {{ getUserDateStr(userInfo.reset_time) }}
              </div>
            </template>
            <div class="item" v-if="stateInfo.membership_type != 'starter'">
              {{ $t('expiresDate') }}: {{ getUserDateStr(userInfo.expire_time) }}
            </div>
          </div>
        </div>
      </div>
      <div class="blackBox going" v-if="userInfo.membership_id_traffic">
        <div class="itemBox">
          <div class="name">{{ $t('packageInfo.traffic') }}</div>
          <div class="barBox">
            <div
              class="process"
              :style="getProWidth(userInfo.seconds_left_traffic, userInfo.seconds_total_traffic)"
            ></div>
          </div>
          <div class="flex-middle flex-wrap">
            <span class="timeOne">{{ getTimeInfo(userInfo.seconds_left_traffic, false) }}</span>
            <span class="time">（{{ getTimeInfo(userInfo.seconds_total_traffic) }}）</span>
          </div>
          <div class="detail">
            <div class="item">
              {{ $t('expiresDate') }}: {{ getUserDateStr(userInfo.expire_time_traffic) }}
            </div>
          </div>
        </div>
      </div>
      <template v-if="nextList.length > 0">
        <h3 class="next flex-middle" style="position: relative;">
          {{ $t('followNext') }}
          <div class="help" :class="{ show: showTip }" @click.stop="showTip = !showTip">
            <div class="tipBox">{{ $t('helpInfo') }}</div>
          </div>
        </h3>
        <div class="blackBox next">
          <div class="itemBox" v-for="info in nextList">
            <div class="name">{{ $t('packageInfo.' + info.membership_type) }}</div>
            <!--                        <div class="item" v-if="info.membership_type == 'pro'">-->
            <!--                            {{info.membership_months > 11 ? $t('yearAuto'):$t('monthAuto')}}-->
            <!--                        </div>-->
            <div class="item">
              {{ getTReplace('monthInUse', info.period_curr + '/' + info.membership_months) }}
            </div>
            <div class="item">
              {{ getTReplace('includeMins', info.membership_type =='unlimited' ? $t('unlimitedtime') : getSToM(info.membership_seconds)) }}
            </div>
          </div>
        </div>
      </template>
      <div class="faqBox">
        <div class="title">{{ $t('faqTitle') }}</div>
        <div class="itemWrap">
          <div class="item" :class="{ active: info.active }" v-for="(info, index) in faqList">
            <div class="nameBox" @click="clickFaq(info)">
              <div class="name" v-html="info.name"></div>
              <div class="imgBox"></div>
            </div>
            <ol>
              <li v-for="d in info.list" v-html="d"></li>
            </ol>
          </div>
        </div>
      </div>
      <div class="linkBox">
        <div class="item" @click="goToPage('faq')">{{ $t('faqBtn') }}</div>
        <div class="line"></div>
        <div class="item" @click="goToPage('agree')">{{ $t('userAgreeBtn') }}</div>
      </div>
    </template>
    <van-action-sheet v-model="popStatus">
      <div class="manageBox">
        <div class="title">{{ $t('manageSub') }}</div>
        <div class="cont" v-html="isAndroid ? $t('manageDetail_Ard') : $t('manageDetail')"></div>
        <div class="btn" @click="popStatus = false">{{ $t('gotBtn') }}</div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import H5Mixins from '../util/H5Mixins';
export default {
  name: 'UserDetailPage',
  mixins: [H5Mixins],
  data() {
    return {
      faqList: [],
      showTip: false,
      userInfo: {},
      stateInfo: {},
      popStatus: false,
    };
  },
  computed: {
    nextList() {
      let { next_data } = this.stateInfo;
      if (next_data && next_data.length > 0) {
        return next_data.filter((item) => !['free', 'starter'].includes(item.membership_type));
      }
      return [];
    },
  },
  mounted() {
    document.addEventListener('click', this.closeTip, false);
    this.getUserInfo();
  },
  beforeUnmount() {
    document.removeEventListener('click', this.closeTip, false);
  },
  methods: {
    getUserInfo() {
      this.isLoading = true;
      this.reqGetInfo('/user/me').then((data) => {
        this.faqList = this.$t('faqList');

        this.isLoading = false;
        let { data_user, data_state } = data;
        this.userInfo = data_user;
        this.stateInfo = data_state;
      });
    },
    getProWidth(l, t) {
      if (!t) {
        return null;
      }
      // let w = ((t - l) / t * 100).toFixed(2);
      let w = ((l / t) * 100).toFixed(2);
      return { width: w + '%' };
    },
    getPackageType() {
      let { expire_time, start_time } = this.userInfo,{membership_flag} = this.stateInfo;
      if(membership_flag == 'Yearly'){//Yearly/Monthly/Unknown
          return this.$t('yearAuto');
      }
      else if(membership_flag == 'Monthly'){
          return this.$t('monthAuto');
      }
      let mus = expire_time - start_time;
      let days = Math.ceil(mus / (60 * 60 * 24));
      return this.$t(days >= 300 ? 'yearAuto' : 'monthAuto');
    },
    getSToM(time) {
      let m = time / 60,
        idx = String(m).indexOf('.');
      // if(idx > -1){
      //     let suff = String(m).substring(idx+1);
      //     if(suff.length > 2){
      //         m = m.toFixed(2);
      //     }
      // }
      m = Math.floor(m);
      return m;
    },
    getTimeInfo(t, istotal = true) {
      let str = '',
        tm = this.getSToM(t);
      if (istotal) {
        str = this.$t('totalTime');
      } else {
        str = this.$t('leftTime');
      }
      return str.replace('${t}', tm);
    },
    getUserDateStr(date) {
      return this.getDateStr(date * 1000, 'yyyy-MM-dd');
    },
    getTReplace(k, value, flag = '${m}') {
      return this.$t(k).replace(flag, value);
    },
    clickFaq(info) {
      if (info.list.length > 0) {
        this.$set(info, 'active', !info.active);
      } else {
        this.goToPage('faq');
      }
    },
    goToPage(type) {
      const isProd = location.hostname.includes('plaud.ai');
      const lang = SysTool.GetQueryString('language') || 'en';
      const baseUrl = isProd
        ? 'https://app.plaud.ai'
        : 'https://plaud-web-dist-test.pages.dev';
      const pages = {
        faq: 'plaud-faq',
        agree: 'user-service-agreement'
      };
      const page = pages[type];
      if (page) {
        window.location.href = `${baseUrl}/shortlink?lang=${lang}&page=${page}`;
      }
    },
    closeTip() {
      this.showTip = false;
    },
  },
};
</script>
<style scoped>
.flexCenter{
  display: flex;
  align-items: center;
}
.free-sign{
  border-radius: 14px;
  display: flex;
  height: 24px;
  padding: 0px 8px;
  align-items: center;
  font-size: 12px;
  border: 0.5px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: border-box;
  &.unlimited{
    background-image: linear-gradient(#050506, #050506),
    linear-gradient(to right, #EDC346, #A66F1B);
    color:#EDC346;
  }
  &.pro{
    color:#FE3BA6;
    background-image: linear-gradient(#050506, #050506),
    linear-gradient(to right, #6675FF, #FE3BA6);
  }
}
</style>
