import Vue from 'vue';
import TransDetailPage from './TransDetailPage';

import RequestMixins from '@/util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from '@/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from '@/components/common/message';
Vue.prototype.setMessage = Message;

import '@/util/Directive';
import i18n from '@/language/h5/index';
Vue.config.productionTip = false;

import styles from '@/styles/transdetail.scss';
new Vue({
  el: '#app',
  i18n,
  template: '<TransDetailPage/>',
  components: { TransDetailPage }
});
