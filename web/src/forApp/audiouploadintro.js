import Vue from 'vue';
import AudioUploadIntroPage from './AudioUploadIntroPage';

import RequestMixins from '@/util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from '@/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from '@/components/common/message';
Vue.prototype.setMessage = Message;

import FirebaseMixins from '@/util/FirebaseMixins';
Vue.mixin(FirebaseMixins);

import '@/util/Directive';
import i18n from '@/language/h5/index';
Vue.config.productionTip = false;

new Vue({
  el: '#app',
  i18n,
  template: '<AudioUploadIntroPage/>',
  components: { AudioUploadIntroPage },
});
