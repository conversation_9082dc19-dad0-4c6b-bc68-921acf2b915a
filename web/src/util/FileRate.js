/*
 * 打分提示框，sessionStorage  没有打过分的才可能出现提示框
 * 模板type，存储在sessionStorage
 * 文件打分状态，存储在localStorage
 * */
export default {
  data() {
    return {
      tipTimer: null,
      summaryRateTip: false, //是否显示总结打分提示语
      hasSummaryRateTip: false, //为了让动画出现
      transRateTip: false,
      hasTransRateTip: false,
      tranTipTimer: null,
      fileRateInfo: {}, //打分过的文件信息存储
    };
  },
  computed: {
    showSummaryTip() {
      if (this.rateInfo && typeof this.rateInfo.summary != 'undefined') {
        return false;
      }
      return true;
    },
    showTransTip() {
      if (this.rateInfo && typeof this.rateInfo.transcription != 'undefined') {
        return false;
      }
      return true;
    },
  },
  mounted() {
    let ratetip = sessionStorage.getItem('summaryRateTip');
    if (ratetip) {
      this.hasSummaryRateTip = false;
    } else {
      this.hasSummaryRateTip = true;
    }
    let trantip = sessionStorage.getItem('transRateTip');
    if (trantip) {
      this.hasTransRateTip = false;
    } else {
      this.hasTransRateTip = true;
    }

    let rateStr = localStorage.getItem('filerate'),
      rateInfo = {};
    if (rateStr) {
      rateInfo = JSON.parse(rateStr);
    }
    this.fileRateInfo = rateInfo;
  },
  beforeDestroy() {
    if (this.tipTimer) {
      clearTimeout(this.tipTimer);
    }
    if (this.tranTipTimer) {
      clearTimeout(this.tranTipTimer);
    }
  },
  methods: {
    setFileTemplateType(fileId, type, isTrans = false) {
      if (type != '') {
        //可能是通过status进来的转写
        let templateStr = sessionStorage.getItem('templatetype'),
          typeInfo = {};
        if (templateStr) {
          typeInfo = JSON.parse(templateStr);
        }
        typeInfo[fileId] = type;
        sessionStorage.setItem('templatetype', JSON.stringify(typeInfo));
      }

      //重新总结了，删掉之前总结打分评论
      if (this.fileRateInfo[fileId] && typeof this.fileRateInfo[fileId].summary != 'undefined') {
        if (isTrans) {
          delete this.fileRateInfo[fileId];
        } else {
          delete this.fileRateInfo[fileId].summary;
        }
        localStorage.setItem('filerate', JSON.stringify(this.fileRateInfo));
        if (this.rateInfo) {
          if (isTrans) {
            this.rateInfo = {};
          } else {
            delete this.rateInfo.summary;
          }
        }
      }
    },
    setRateLogEvent(tab, fileID, action, reasons = [], plaudStatus = true) {
      //action:1赞 0踩
      let template = '',
        myTab = ['transcription', 'summary'][tab];
      if (this.fileRateInfo[fileID] && this.fileRateInfo[fileID][myTab]) {
        this.setMessage(this.$t('Score_dialog_toast_sent_repeat'));
        return false;
      }
      if (tab === 1) {
        let templateStr = sessionStorage.getItem('templatetype');
        if (templateStr) {
          let tempInfo = JSON.parse(templateStr),
            type = tempInfo[fileID];
          if (type) {
            template = type;
          }
        }
      }
      let reasonArr = [];
      if (action === 0 && reasons.length > 0) {
        reasons.forEach((item) => {
          reasonArr.push(`'${item}'`);
        });
      }
      let fileRate = this.fileRateInfo[fileID];
      if (!fileRate) {
        fileRate = {};
      }
      fileRate[myTab] = action;
      this.fileRateInfo[fileID] = fileRate;
      localStorage.setItem('filerate', JSON.stringify(this.fileRateInfo));
      let params = { template, action, reason: reasonArr.join(','), fileID, tab: myTab };
      if (!plaudStatus) {
        params.fileID = '';
      }
      this.setPlaudLogEvent('rate', params);
      this.setMessage(this.$t('Score_dialog_toast_sent_bad'));
    },
    seeSummaryRateTip() {
      //
      if (this.tipTimer) {
        clearTimeout(this.tipTimer);
      }
      if (this.hasSummaryRateTip && this.showSummaryTip) {
        this.summaryRateTip = true;
        this.tipTimer = setTimeout(() => {
          sessionStorage.setItem('summaryRateTip', 'true');
          this.hasSummaryRateTip = false;
        }, 3200);
      }
    },
    seeTranRateTip() {
      //
      if (this.tranTipTimer) {
        clearTimeout(this.tranTipTimer);
      }
      if (this.hasTransRateTip && this.showTransTip) {
        this.transRateTip = true;
        this.tranTipTimer = setTimeout(() => {
          sessionStorage.setItem('transRateTip', 'true');
          this.hasTransRateTip = false;
        }, 3200);
      }
    },
    sendRate(flag, action) {
      //
      let key = ['transcription', 'summary'][flag];
      if (typeof this.rateInfo[key] != 'undefined') {
        this.setMessage(this.$t('Score_dialog_toast_sent_repeat'));
        return false;
      }
      if (action === 1) {
        this.$set(this.rateInfo, key, action);
        this.setRateLogEvent(flag, this.info.id, 1);
      } else {
        this.$refs.DoScore.handlerVisible(true, flag);
      }
    },
  },
};
