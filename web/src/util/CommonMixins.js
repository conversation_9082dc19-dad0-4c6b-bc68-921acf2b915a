Date.prototype.Format1 = function (fmt) {
  //author: meizz 默认yyyy-MM-dd
  if (!fmt) {
    fmt = 'yyyy-MM-dd';
  }
  var o = {
    'M+': this.getMonth() + 1, //月份
    'd+': this.getDate(), //日
    'h+': this.getHours() % 12 == 0 ? 12 : this.getHours() % 12, //小时
    'H+': this.getHours(), //小时
    'm+': this.getMinutes(), //分
    's+': this.getSeconds(), //秒
    'q+': Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
    a: this.getHours() > 12 ? '下午' : '上午',
    EEEEE: '周' + ['日', '一', '二', '三', '四', '五', '六'][this.getDay()],
  };
  if (/(y+)/.test(fmt))
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
  for (var k in o)
    if (new RegExp('(' + k + ')').test(fmt))
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
  fmt = fmt.replace('周', '星期');
  return fmt;
};
import TagMixins from '../util/TagMixins';
export default {
  mixins: [TagMixins],
  data() {
    return {
      speakerColors: ['#F9A251', '#9692C9', '#70A2EC', '#3DC8C8'],
      speakerMap: {},
      // isEnvPro: __PROD__, //正式环境
      speakerTrans: [], //speaker对应的trans
    };
  },
  computed: {
    hasSpeaker() {
      return Object.values(this.speakerMap).length;
    },
  },
  methods: {
    getTimeStr(time, hour = false, ism = true, hasMin = false) {
      //ism是不是毫秒
      if (!time && time !== 0) {
        return '';
      }
      if (ism) {
        time = Math.floor(time / 1000);
      }
      let m = Math.floor(time / 60),
        h = 0; //向下取整
      if (m >= 60) {
        h = Math.floor(m / 60);
        m = m - h * 60;
      }
      let isZero = (num) => {
        return (num < 10 ? '0' : '') + num;
      };
      let s = Math.floor(time - m * 60 - h * 3600); //四舍五入
      let str = isZero(s);
      if (m > 0 || hasMin) {
        str = isZero(m) + ':' + str;
      }
      if (h > 0 || hour) {
        if (!str.includes(':')) {
          str = isZero(m) + ':' + str;
        }
        str = isZero(h) + ':' + str;
      }
      return str;
    },
    getTimeName(times, fix = ['h', 'm', 's']) {
      let arr = times.split(':').map(Number),
        str = [];
      for (let i = arr.length; i--; i >= 0) {
        str.unshift(arr[i] + fix[fix.length - 1 - str.length]);
      }
      return str.join(' ');
    },
    getDateStr(dateStr, format = 'yyyy-MM-dd HH:mm:ss') {
      if (!dateStr) {
        return '';
      }

      let date = new Date(dateStr);
      return date.Format1(format);
    },
    getSizeStr(num) {
      let k = num / 1024,
        sub = 'KB';
      let toStr = (n) => {
        if (String(n).includes('.')) {
          return n.toFixed(1);
        }
        return n;
      };
      if (k > 1024) {
        k = k / 1024;
        sub = 'MB';
      }
      return toStr(k) + sub;
    },
    getNumZero(num, fixed = 1) {
      if (String(num).includes('.')) {
        return num.toFixed(fixed);
      }
      return num;
    },
    getStrLength(str) {
      let mystr = str.replace(/[\u4e00-\u9fa5]/g, '**');
      return mystr.length;
    },
    getNameEllipse(str, count = 10) {
      if (str.replace(/[\u4e00-\u9fa5]/g, '**').length <= count) {
        return str;
      }
      let len = 0;
      let tmpStr = '';
      for (let i = 0; i < str.length; i++) {
        // 遍历字符串
        if (/[\u4e00-\u9fa5]/.test(str[i])) {
          // 中文 长度为两字节
          len += 2;
        } else {
          len += 1;
        }
        if (len > count) {
          break;
        } else {
          tmpStr += str[i];
        }
      }
      return tmpStr + '...';
    },
    getTranslateStr(key, sign, signv) {
      if (sign == '20240327') {
        sign = '%s';
      }
      let str = this.$t(key);
      str = str.replace(/\n/g, '<br/>');
      str = str.replace(/\$b/g, '<b class="tipWeight">');
      str = str.replace(/\$p/g, '</b>');
      if (sign) {
        return str.replace(sign, signv);
      }
      return str;
    },
    getTranslateStrs(key, sign, values = []) {
      if (sign == '20240327') {
        sign = '%s';
      }
      let str = this.$t(key);
      str = str.replace(/\n/g, '<br/>');
      str = str.replace(/\$b/g, '<b class="tipWeight">');
      str = str.replace(/\$p/g, '</b>');
      values.forEach((item) => {
        str = str.replace(sign, item);
      });
      return str;
    },
    getTranslateStrsNodes(input, config) {
      const regex = /\$as\s*(.*?)\s*\$ae/g;
      let index = 0;

      // 替换占位符
      let result = input.replace(regex, function (match, text) {
        if (index >= config.length) {
          console.log('getTranslateStrsNodes', '配置对象中的项数不足以替换所有占位符');
        }
        const { href, className } = config[index++];
        return `<a href='${href}' data-pld='common-${className}' class='${className}' target="_blank">${text}</a>`;
      });

      return result;

      // 测试函数
      /*
      const inputString = "Support file with maximum length of $as 5h $ae for a single $as hi $ae recording.";
      const config = [
          { href: 'www.plaud.ai', className: 'aline' },
          { href: 'www.baidu.com', className: 'mt10' }
      ];
      */
    },
    parseLocalizedString(key, values, config) {
      let str = this.$t(key);
      str = str.replace(/\n/g, '<br/>');
      str = str.replace(/\$bs/g, `<b class="${config.bClass ? config.bClass : 'tipWeight'}">`);
      str = str.replace(/\$be/g, '</b>');
      str = str.replace(
        /\$as/g,
        `<a href="${config.aHref ? config.aHref : ''}" class="${config.aClass ? config.aClass : ''}">`,
      );
      str = str.replace(/\$ae/g, '</a>');

      values.forEach((item) => {
        str = str.replace('%s', item);
      });

      return str;
    },
    domClick() {
      document.body.click();
    },
    getIconCode(code) {
      if (code && code.indexOf('e6') === 0) {
        return `&#x${code};`;
      }
      return '';
    },
    /* judgePassword(pwd) {
      // return /^[a-zA-Z0-9!@#$%^&*(),.?":{}|<>]{6,16}$/
      return /^[a-zA-Z0-9!@#$%^&*(),.?":{}|<>]+$/.test(pwd);
      // return /^[A-Za-z0-9!@#$%&_\-.]+$/.test(pwd);
    }, */
    judgePassword(pwd, regex) {
      return regex.test(pwd);
    },
    isNotEmail(email) {
      return !/[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,10}$/.test(email);
    },
    getMinNum(seconds) {
      return Math.floor(seconds / 60);
    },
    getPerNum(left, total) {
      if (total <= 0) {
        return 0;
      }

      let w = Math.floor((left / total) * 100);
      return Math.min(w,100);
    },
    initSpeakerInfo() {
      this.speakerMap = {};
      this.speakerTrans = [];
    },
    setSpeakerColor(transInfo = this.transInfo, hasSpeakerTrans = true, batchMap, single = false) {
      //分享不需要传false,single只修改当前
      if (transInfo.length > 0 && transInfo[0].speaker) {
        let len = this.speakerColors.length;
        if (batchMap) {
          for (let n in batchMap) {
            //批量修改，speaker对应颜色不变,替换name
            if (single) {
              let speakerTrans = this.speakerTrans.find((item) => item.speaker === n);
              if (speakerTrans.list.length === 1) {
                //如果只有一个转写，即使是修改当前，也是直接换颜色的name就行
                single = false;
              }
            }
            if (!single) {
              this.speakerMap[batchMap[n]] = this.speakerMap[n];
              delete this.speakerMap[n];
            } else {
              this.speakerMap[batchMap[n]] =
                this.speakerColors[Object.values(this.speakerMap).length % len];
            }
          }
        } else {
          let names = transInfo.map((item) => item.speaker);
          let nameArr = Array.from(new Set(names)),
            myMap = {};
          nameArr.forEach((name, index) => {
            myMap[name] = this.speakerColors[index % len];
          });
          this.speakerMap = myMap;
        }

        //2个，先找出超过60个字符的，如果没有就找最大字符的，然后将找出来的这2个按时间顺序排列
        if (hasSpeakerTrans) {
          let mySTMap = {};
          transInfo.forEach((item) => {
            let time = item.end_time - item.start_time;
            let nameMap = mySTMap[item.speaker];
            if (!nameMap) {
              nameMap = {
                total: 0,
                limit: 0,
                lens: [],
                more: false,
                speaker: item.speaker,
                newname: '',
                list: [],
              };
            }
            nameMap.total += time;
            if (nameMap.limit < 2) {
              if (nameMap.list.length < 2) {
                nameMap.list.push(item);
                nameMap.lens.push(item.content.length);
              } else {
                let idx = nameMap.lens.lastIndexOf(Math.min(...nameMap.lens)),
                  len = nameMap.lens[idx];
                if (item.content.length > 60 || item.content.length > len) {
                  nameMap.list.splice(idx, 1);
                  nameMap.list.push(item);
                  nameMap.lens.splice(idx, 1);
                  nameMap.lens.push(item.content.length);
                }
                nameMap.more = true;
              }
              if (item.content.length > 60) {
                nameMap.limit += 1;
              }
            } else {
              nameMap.more = true;
            }
            mySTMap[item.speaker] = nameMap;
          });
          let mapList = Object.values(mySTMap);
          mapList.sort((a, b) => b.total - a.total);
          this.speakerTrans = mapList;
        }
      } else {
        this.speakerMap = {};
        if (hasSpeakerTrans) {
          this.speakerTrans = [];
        }
      }
    },
  },
};
