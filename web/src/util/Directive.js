// Vue 3 directive definition
export const vstop = {
  mounted: function (el) {
    if (!el.querySelector('.noPopEvt')) {
      el.addEventListener(
        'click',
        function (event) {
          event.stopPropagation();
        },
        false,
      );
    }
  },
};

// Function to register directives with Vue 3 app
export function registerDirectives(app) {
  app.directive('vstop', vstop);
}
