<template>
  <MyDialog
    :title="$t('Score_dialog_summary_title')"
    width="592px"
    :visible="dialogStatus"
    @close="handlerVisible(false)"
  >
    <template v-slot:content>
      <div>
        <div class="rateBox" v-if="type !== ''">
          <div class="descTxt">{{ $t('Score_dialog_summary_subtitle') }}</div>
          <div class="tipItems">
            <div
              class="tipItem"
              v-for="i in tipLen"
              @click="clickRate(i)"
              :class="{ active: rates.includes(i) }"
            >
              {{ $t(tipKey + i) }}
            </div>
          </div>
          <textarea
            class="textareaIpt"
            v-model="otherInput"
            :placeholder="$t('Score_dialog_summary_hint')"
          ></textarea>
          <div class="formError">{{ errorMsg }}</div>
          <div class="checkBtn">
            <div class="flex-middle">
              <div
                class="lsCheck"
                :class="{ active: plaudStatus }"
                @click="plaudStatus = !plaudStatus"
              ></div>
              {{ [$t('Score_dialog_transcription_agree'), $t('Score_dialog_summary_agree')][type] }}
            </div>
            <div class="commonBtn" @click="save" style="width: 120px">
              {{ $t('Score_dialog_btn_send') }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </MyDialog>
</template>

<script>
import MyDialog from '../../common/MyDialog.vue';
export default {
  name: 'DoScore',
  components: { MyDialog },
  data() {
    return {
      dialogStatus: false,
      type: '',
      rates: [],
      plaudStatus: true,
      tipLen: 4,
      tipKey: '',
      otherInput: '',
      errorMsg: '',
    };
  },
  methods: {
    handlerVisible(status, type) {
      if (!status && this.dialogStatus) {
        this.$set(this.$parent.rateInfo, ['transcription', 'summary'][this.type], 0);
        this.$parent.setRateLogEvent(this.type, this.$parent.info.id, 0, [], this.plaudStatus);
      }
      this.dialogStatus = status;
      this.type = type;
      if (type == 1) {
        this.tipLen = 5;
        this.tipKey = 'Score_dialog_summary_tip';
      } else {
        this.tipLen = 4;
        this.tipKey = 'Score_dialog_transcription_tip';
      }
      this.rates = [];
      this.otherInput = '';
      this.errorMsg = '';
      this.plaudStatus = true;
    },
    clickRate(i) {
      let idx = this.rates.indexOf(i);
      if (idx > -1) {
        this.rates.splice(idx, 1);
      } else {
        this.rates.push(i);
      }
    },
    save() {
      this.rates.sort();
      let reasons = [];
      this.rates.forEach((index) => {
        reasons.push(this.$t(this.tipKey + index));
      });
      if (this.otherInput) {
        if (this.otherInput.length > 10000) {
          this.errorMsg = this.$t('Folder_toolong');
          return false;
        }
        reasons.push(this.otherInput);
      }
      this.$set(this.$parent.rateInfo, ['transcription', 'summary'][this.type], 0);
      this.$parent.setRateLogEvent(this.type, this.$parent.info.id, 0, reasons, this.plaudStatus);
      this.dialogStatus = false;
    },
  },
};
</script>
