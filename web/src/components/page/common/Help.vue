<template>
  <div class="userCenterBox" style="margin-top: 56px">
    <h4>{{ $t('Me_help_tilte') }}</h4>
    <div class="uline"></div>
    <div class="helpBox" style="padding-bottom: 20px">
      <h6>{{ $t('Me_help_hotquestion') }}</h6>
      <div class="helpItems">
        <div class="helpItem" v-for="info in hotList" @click="goLink(info.href)">
          <div class="helpName">{{ info.name }}</div>
          <div class="iconfont icon-next"></div>
        </div>
      </div>
      <div class="helpItem" style="margin-top: 16px" @click="goLink('https://support.plaud.ai/')">
        <!-- @click="goLink('https://store.plaud.ai/pages/app-faq')" -->
        <div class="helpName">
          <div class="iconfont icon-about"></div>
          {{ $t('Me_help_hotquestion_all') }}
        </div>
        <div class="iconfont icon-next"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Help',
  data() {
    return {
      hotList: [
        {
          name: this.$t('Me_help_hotquestion_questionone'),
          // href: 'https://store.plaud.ai/pages/app-bind-device',
          href: 'https://support.plaud.ai/hc/en-us/articles/9195212748303-Bind-Your-Device',
        },
        {
          name: this.$t('Me_help_hotquestion_questiontwo'),
          // href: 'https://store.plaud.ai/pages/app-recording-guide',
          href: 'https://support.plaud.ai/hc/en-us/articles/9195246072847-Start-Recording',
        },
        {
          name: this.$t('Me_help_hotquestion_questionthree'),
          // href: 'https://store.plaud.ai/pages/app-transcribe-guide',
          href: 'https://support.plaud.ai/hc/en-us/articles/9195383345807-Transcription',
        },
        {
          name: this.$t('Me_help_hotquestion_questionfour'),
          // href: 'https://store.plaud.ai/pages/app-summary-guide',
          href: 'https://support.plaud.ai/hc/en-us/articles/9195391147919-Summary-Mind-map',
        },
        {
          name: this.$t('zapier_help_text'),
          href: 'https://support.plaud.ai/hc/en-us/articles/12200669941647-PLAUD-Zapier-Integration',
        },
      ],
    };
  },
  created() {
    // this.initCustomerService();
    zE('messenger', 'close');
    zE('messenger', 'show');
  },
  mounted() {
    // this.toggleCustomerService(true, '');
  },
  beforeDestroy() {
    zE('messenger', 'close');
    zE('messenger', 'hide');
    // this.toggleCustomerService(false, 'none');
  },
  methods: {
    goLink(url) {
      window.open(url, '_blank');
    },
    initCustomerService() {
      // 创建 script 元素
      var script = document.createElement('script');
      script.id = 'ze-snippet';
      script.src =
        'https://static.zdassets.com/ekr/snippet.js?key=696b9176-2236-4cc6-b5b2-476bdf4fb38c';
      script.defer = true;

      // 将 script 元素添加到文档的 head 中
      document.head.appendChild(script);
    },
    toggleCustomerService(flag = true, visibleStr = '') {
      let valueStr = flag ? 'block' : 'none';
      let launcherRef = document.getElementById('launcher');
      launcherRef.style.display = valueStr;

      let iframes = document.getElementsByName('Messaging window');
      if (iframes.length > 0) {
        let iframeRef = iframes[0];
        // iframeRef.style.visibility = visibleStr;
        iframeRef.style.display = visibleStr;
      }

      const closeMessage = document.querySelector('iframe[title="Close message"]');
      if (closeMessage) {
        closeMessage.style.display = visibleStr;
      }

      const messageFromCompany = document.querySelector('iframe[title="Message from company"]');
      if (messageFromCompany) {
        messageFromCompany.style.display = visibleStr;
      }
    },
  },
};
</script>
