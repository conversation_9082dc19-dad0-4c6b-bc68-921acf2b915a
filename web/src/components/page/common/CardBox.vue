<!-- 卡片 -->
<template>
  <div
    class="card-box group h-full flex flex-col relative bg-white border border-[#E4E7EC] hover:border-transparent rounded-[14px]"
    :class="isActive ? 'active' : ''"
  >
    <Badges
      v-if="data?.badgeText || data?.isLocked"
      :text="$t(data?.badgeText)"
      :status="data?.badgeStatus"
      :isLocked="data?.isLocked"
    />

    <!-- 预览放大的图标 -->
    <img
      v-if="!(data.custom_type === 'custom' && data.id === 'create')"
      :src="getImagePath('icon_expand')"
      class="w-6 h-6 absolute top-[6px] right-[6px] opacity-0 group-hover:opacity-100 transition-opacity duration-200"
      @click.stop="handleExpand"
    />

    <img
      v-if="isActive"
      :src="getImagePath('check_mark')"
      class="w-[17px] h-[17px] absolute bottom-[1px] right-[1px]"
    />

    <div
      @click="handleClick"
      class="flex flex-col justify-between bg-white px-3 py-2 border-4 border-transparent m-[1px] rounded-[14px] h-full overflow-hidden"
    >
      <template v-if="data.custom_type === 'custom' && data.id === 'create'">
        <div
          class="submit-card w-full h-full flex flex-col items-center justify-center rounded-[14px]"
        >
          <img src="/static/template_icon_create.png" class="w-8 h-8 mb-1" />
          <div class="cseTxt font-bold text-[14px]">
            {{ $t('Folder_create_button') }}
          </div>
        </div>
      </template>
      <template v-else>
        <div class="flex flex-col">
          <div class="flex items-center mb-1">
            <span class="text-[24px] font-bold mr-1 text-[#D0D5DD] leading-8" v-if="showIndex">{{
              index + 1
            }}</span>
            <span
              v-if="data.custom_type === 'community'"
              class="material-symbols-rounded flex-shrink-0"
              :style="{
                color: data?.iconColor,
              }"
              >{{ data?.iconName }}</span
            >
            <img
              v-else-if="data.custom_type === 'custom'"
              src="/static/template_icon_customize.png"
              class="w-8 h-8 mr-2"
            />
            <img
              :src="data?.icon"
              class="w-8 h-8 mr-2 color-[#858C9B] text-8 leading-8"
              v-else-if="data?.icon"
            />
            <img
              src="/static/template_icon_blank.png"
              class="w-8 h-8 mr-2 color-[#858C9B] text-8 leading-8"
              v-else
            />
          </div>
          <div class="h-[60px] overflow-hidden">
            <SmartEllipsis
              v-if="data.custom_type === 'community'"
              :type="data.custom_type"
              :textOne="data?.title"
              :textTwo="data?.description"
            ></SmartEllipsis>
            <SmartEllipsis
              v-else-if="data.custom_type === 'system'"
              :type="data.custom_type"
              :textOne="data?.name"
              :textThree="data?.keywords"
            ></SmartEllipsis>
            <SmartEllipsis
              v-else
              :type="data.custom_type"
              :textOne="data?.name"
              :textTwo="data?.content"
            ></SmartEllipsis>
          </div>
        </div>
        <div class="flex items-center text-[12px] text-[#858C9B] h-[18px] leading-[18px] mt-1">
          <template v-if="data.custom_type === 'community'">
            <!-- <svg-icon name="dashboard" class="w-4 h-4 mr-[2px]"></svg-icon> -->
            <img :src="getImagePath('dashboard')" class="w-4 h-4 mr-[2px]" />
            <span class="mr-2">{{ data.usageCount }}</span>
            <span class="mr-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
            <span class="truncate whitespace-nowrap">{{ data.authorName }}</span>
          </template>
          <template v-else-if="data.custom_type === 'system'">
            <!-- <svg-icon name="plaud_icon" class="w-4 h-4 mr-1"></svg-icon> -->
            <img :src="getImagePath('plaud_icon')" class="w-4 h-4 mr-1" />
            <span class="text-[12px]">PLAUD.AI</span>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import SmartEllipsis from './SmartEllipsis.vue';
import Badges from './Badges.vue';

export default {
  name: 'CardBox',
  components: { SmartEllipsis, Badges },
  props: {
    showIndex: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    getImagePath(icon) {
      try {
        return require(`../../../../public/static/${icon}.svg`);
      } catch (e) {
        console.error(`Image not found: ../../../../public/static/${icon}.svg`, e);
        return '';
      }
    },
    handleExpand() {
      this.$emit('expand');
    },
    handleClick() {
      this.$emit('handleClick');
    },
  },
  mounted() {},
};
</script>

<style scoped lang="less">
.active {
  border: 1px solid transparent;
  padding: 0;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image:
    linear-gradient(to right, #c366f2, #ff89ae), linear-gradient(90deg, #8b8cf9, #fa8865);
}
</style>
