<!-- 转写总结弹窗 -->
<template>
  <!-- 第一个弹窗 -->

  <el-dialog
    width="440px"
    :visible="dialogStatus"
    top="5vh"
    :modal="true"
    custom-class="dialogWrap"
    :show-close="false"
    v-if="initSmall && dialogStatus"
  >
    <template v-slot:title>
      <div class="titleDialogBox" v-if="dialogStatus && !upgradeStatus">
        <div class="titleMore">
          <div class="iconfont icon-Audio"></div>
          <div class="info">
            <div class="name">{{ getNameEllipse(fileInfo.filename, 40) }}</div>
            <div class="time">{{ getTimeName(getTimeStr(fileInfo.duration, false)) }}</div>
          </div>
        </div>
        <div class="iconfont icon-Close" @click="closePop()"></div>
      </div>
    </template>
    <slot name="content">
      <div class="transSummary2" @click="showLimitPop = false" v-show="!upgradeStatus">
        <VuePerfectScrollbar class="flexBox">
          <div class="tsTitle">
            {{ $t('Filedetail_submit_summary_template_title') }}
          </div>
          <div class="tsList">
            <div class="tsMinBox">
              <div
                class="tsItem"
                v-for="info in simpleList"
                :class="{
                  active:
                    info.custom_type === 'community'
                      ? summaryType == info[translateKey].id
                      : summaryType == info.type || summaryType == info.id,
                }"
                @click="doSelectTemplate('simpleDialog', info)"
              >
                <div class="flex-middle">
                  <span
                    v-if="info.custom_type === 'community'"
                    class="material-symbols-rounded flex-shrink-0 imgIcon"
                    :style="{
                      color: info[translateKey]?.iconColor,
                    }"
                    >{{ info[translateKey]?.iconName }}</span
                  >
                  <img :src="info.icon" class="imgIcon" v-else-if="info.icon" />
                  <img
                    src="/static/template_icon_customize.png"
                    class="imgIcon"
                    v-else-if="info.id"
                  />
                  <img src="/static/template_icon_blank.png" class="imgIcon" v-else />
                  <div class="tsMiddle">
                    <div class="tsTop">
                      <div class="tsname" v-if="info.type_type === 'community'">
                        {{ info[translateKey].name }}
                      </div>
                      <div class="tsname" v-else>{{ info.name }}</div>
                      <div class="tstag test" v-if="info.tag && info.tag.includes('test')">
                        test
                      </div>
                      <div class="tstag test" v-else-if="info.tag && info.tag.includes('beta')">
                        Beta
                      </div>
                      <div
                        class="tstag new"
                        v-if="
                          info.calc_type &&
                          info.calc_type == 'NEW' &&
                          !lastUseList.includes(info.type)
                        "
                      >
                        New
                      </div>
                      <div
                        class="tstag last flex-shrink-0"
                        v-else-if="info.calc_type && info.calc_type == 'NEWEST'"
                      >
                        {{ $t('Temlate_simple_last') }}
                      </div>
                      <div
                        class="tstag most"
                        v-else-if="info.calc_type && info.calc_type == 'MAXUSED'"
                      >
                        {{ $t('Temlate_simple_most') }}
                      </div>
                      <div
                        class="tstag try"
                        v-else-if="info.custom_id && info.custom_id == 'tryIt'"
                      >
                        {{ $t('try_text') }}
                      </div>
                      <div class="tstag lock flex-shrink-0" v-if="info.is_locked">
                        {{ $t('Upgrade_icon_text') }}
                      </div>
                    </div>
                    <div class="keyWords" v-if="info.keywords && info.keywords.length > 0">
                      <div class="wordItem" v-for="w in info.keywords">{{ w }}</div>
                    </div>
                    <div
                      class="description text-[10px] font-medium text-[#667085]"
                      v-if="info?.type_type !== 'community' && info.description"
                    >
                      {{ info.description }}
                    </div>
                    <div
                      class="description text-[10px] font-medium text-[#667085]"
                      v-if="info?.type_type === 'community' && info[translateKey].description"
                    >
                      {{ info[translateKey].description }}
                    </div>
                  </div>
                </div>
                <div class="templateChecked"></div>
              </div>
              <Loading :status="isLoading" />
            </div>
            <div class="tsItem" @click="doViewAll()">
              <div
                class="imgIcon iconfont icon-template_icon_view_all"
                style="color: #bfaaf2"
              ></div>
              <div class="tsMiddle" style="margin-top: 3px">
                <span class="tsname">{{ $t('Summary_template_viewall') }}</span>
              </div>
              <div class="flex-middle" style="margin-right: 4px">
                <div class="redDian" v-if="firstViewAll"></div>
                <div
                  class="iconfont icon-next nextIcon"
                  style="color: #1f1f1f; font-size: 14px"
                ></div>
              </div>
            </div>
            <div class="tsLine"></div>
            <el-popover
              popper-class="elPopoverClass no-transition"
              placement="right-start"
              width="320"
              trigger="click"
              :open-delay="0"
              :close-delay="0"
              @hide="hideModelAi()"
            >
              <VuePerfectScrollbar class="moreLangList modelai">
                <div
                  class="moreItem"
                  :class="{ active: transAi == info.id, last: index === transAiList.length - 1 }"
                  @click="doSelectAi(info.id, true)"
                  v-for="(info, index) in transAiList"
                >
                  <div>
                    <div class="flex-middle">
                      {{ info.name }}
                      <div class="beta" v-if="info.isBeta">
                        {{ $t('Filedetail_submit_transcription_distinguish_speaker_tip') }}
                      </div>
                      <div class="beta" v-if="info.is_inner">Inner</div>
                    </div>
                    <div class="model_desc">{{ info.des }}</div>
                  </div>
                  <div class="iconfont icon-Checkmark"></div>
                </div>
              </VuePerfectScrollbar>
              <template v-slot:reference>
                <div class="tsItem">
                  <div class="imgIcon iconfont icon-template_icon_ai_model"></div>
                  <div class="tsMiddle">
                    <div class="tsLabel">{{ $t('Summary_template_model_title') }}</div>
                    <span class="tsname">{{ transAiInfo.name }}</span>
                  </div>
                  <div class="flex-middle">
                    <div class="redDian" v-if="transAiList[0].isred"></div>
                    <div class="iconfont icon-sidebar_clsoure nextIcon"></div>
                  </div>
                </div>
              </template>
            </el-popover>
            <el-popover
              popper-class="elPopoverClass no-transition"
              placement="right-end"
              width="260"
              :open-delay="0"
              :close-delay="0"
              trigger="click"
            >
              <VuePerfectScrollbar class="moreLangList">
                <div
                  class="moreItem"
                  :class="{ active: selectLang == lng.id, highMoreItem: showAutoTips(lng) }"
                  @click="doSelectLang(lng.id, true)"
                  v-for="lng in langList"
                  v-if="enableSummaryAuto || (pageFlag != 2 && lng.id != 'auto') || pageFlag == 2"
                >
                  <div>
                    {{ lng.name }}
                    <!-- 非灰度场景（非开放转写auto场景），总结Auto时保持跟随转写原因 -->
                    <div class="desc" v-if="showAutoTips(lng)">
                      {{ $t('Filedetail_resubmit_summary_language_autotips') }}
                    </div>
                  </div>
                  <div class="iconfont icon-Checkmark"></div>
                </div>
              </VuePerfectScrollbar>
              <template v-slot:reference>
                <div class="tsItem">
                  <div class="imgIcon iconfont icon-template_icon_language"></div>
                  <div class="tsMiddle">
                    <div class="tsLabel">
                      {{ $t('Filedetail_submit_transcription_language_title') }}
                    </div>
                    <span class="tsname">{{ selectLangInfo.name }}</span>
                  </div>
                  <div class="iconfont icon-sidebar_clsoure nextIcon"></div>
                </div>
              </template>
            </el-popover>
            <div class="tsItem nohover relative">
              <div class="imgIcon iconfont icon-template_icon_speaker_labels"></div>
              <div class="tsMiddle">
                <div class="flex-middle">
                  <span class="tsname">{{
                    $t('Filedetail_submit_transcription_distinguish_speaker')
                  }}</span>
                  <div class="beta">
                    {{ $t('Filedetail_submit_transcription_distinguish_speaker_tip') }}
                  </div>
                </div>
                <div class="noSupportBox" v-if="fileInfo.duration > maxTimes">
                  <div class="transSummaryPop speaker" v-if="showLimitPop">
                    {{ $t('Speaker_limit_3hour') }}
                    <div class="closeBtn" @click="showLimitPop = false">
                      <div class="iconfont icon-icon_delete_upload_file"></div>
                    </div>
                  </div>
                  {{ $t('Speaker_limit_nosupported') }}
                  <div
                    class="iconfont tipicon icon-a-Info2"
                    @click.stop="showLimitPop = true"
                  ></div>
                </div>
              </div>
              <el-switch
                v-model="speakerStatus"
                :width="36"
                active-color="#1F1F1F"
                inactive-color="#D0D5DD"
                @change="changeSpeakerStatus"
              ></el-switch>
            </div>
          </div>
        </VuePerfectScrollbar>
        <div class="commonBtn" @click="reTran()" style="margin-top: 10px">
          <div class="cseTxt flex-between">
            {{ $t('Filedetail_submit_generate_now') }}
          </div>
        </div>
      </div>
    </slot>
  </el-dialog>
  <!-- view all弹窗 -->
  <el-dialog
    width="1120px"
    :visible="dialogStatus"
    :modal="true"
    top="5vh"
    custom-class="dialogWrap viewAllDialog"
    class="overflow-hidden"
    :show-close="false"
    v-else-if="dialogStatus"
  >
    <template v-slot:title>
      <div
        class="h-[50px] flex items-center justify-between px-4 bg-[#f0f2f6]"
        v-if="dialogStatus && !defineStatus && !upgradeStatus"
      >
        <div class="text-[18px] leading-[28px] font-bold text-[#1F1F1F]">
          {{ $t('Filedetail_submit_summary_template_title') }}
        </div>
        <div class="iconfont icon-Close" @click="closePop()"></div>
      </div>
    </template>
    <slot name="content">
      <div
        class="transSummary2 all bg-[#f0f2f6]"
        @click="showLimitPop = false"
        v-show="!defineStatus && !upgradeStatus"
      >
        <div class="infoCont">
          <!-- 左侧 -->
          <VuePerfectScrollbar class="infoCont_left bg-[#f0f2f6]" ref="contLeft">
            <!-- 分类列表 -->
            <div class="leftList">
              <!-- 最近使用 -->
              <div
                class="p-2 flex items-center justify-between rounded-[8px] text-[14px] leading-[20px] mt-[6px] hover:bg-[#E4E7EC] cursor-pointer"
                :class="[
                  selectName == recentCategory ? 'bg-[#E4E7EC]' : '',
                  'category_item_' + recentCategory,
                ]"
                @click="changeCategory(recentCategory)"
              >
                <div class="flex items-center">
                  <img :src="getImagePath('recently_use')" class="w-4 h-4 mr-[6px]" />
                  <span class="font-bold text-[#1F1F1F]">{{ $t('recently_used_text') }}</span>
                </div>
                <img :src="getImagePath('chevron_right_icon')" class="w-4 h-4 ml-2" />
              </div>
              <!-- 模版分类列表 -->
              <div
                v-for="(tempInfo, index) in summaryList"
                :key="index"
                class="p-2 flex items-center justify-between rounded-[8px] text-[14px] leading-[20px] mt-[6px] hover:bg-[#E4E7EC] cursor-pointer"
                :class="[
                  selectName == tempInfo.category ? 'bg-[#E4E7EC]' : '',
                  'category_item_' + tempInfo.category,
                ]"
                @click="changeCategory(tempInfo.category)"
              >
                <div class="flex items-center">
                  <img :src="tempInfo?.icon" class="w-5 h-5 mr-[6px]" v-if="tempInfo?.icon" />
                  <img src="/static/template_icon_blank.png" class="w-5 h-5 mr-[6px]" v-else />
                  <span class="font-bold text-[#1F1F1F]">{{ tempInfo?.category }}</span>
                </div>
                <img :src="getImagePath('chevron_right_icon')" class="w-4 h-4 ml-2" />
              </div>
              <!-- customize -->
              <div
                class="p-2 flex items-center justify-between rounded-[8px] text-[14px] leading-[20px] mt-[6px] hover:bg-[#E4E7EC] cursor-pointer"
                :class="[
                  selectName == customCategory ? 'bg-[#E4E7EC]' : '',
                  'category_item_' + customCategory,
                ]"
                @click="changeCategory(customCategory)"
              >
                <div class="flex items-center">
                  <img src="/static/template_icon_customize.png" class="w-5 h-5 mr-[6px]" />
                  <span class="font-bold text-[#1F1F1F]">{{ $t('cusomize_text') }}</span>
                </div>
                <img :src="getImagePath('chevron_right_icon')" class="w-4 h-4 ml-2" />
              </div>
              <!-- :class="[summaryType == info.type ? 'active' : '', 'template_item_' + info.type]" -->
              <!-- <div class="tsList">
                <div
                  class="tsItem"
                  v-for="info in tempInfo.data"
                  @click="doSelectTemplate(info)"
                  :class="[
                    summaryType == info.type ? 'active' : '',
                    summaryType == info.id ? 'active' : '',
                    'template_item_' + info.type,
                  ]"
                >
                  <div class="flex-middle">
                    <span
                      class="material-symbols-rounded mr-2"
                      :style="{
                        color: formatColor(info.latest_published_version.icon_color),
                      }"
                      v-if="info.custom_type === 'community'"
                      >{{ formatIcon(info.latest_published_version.icon) }}</span
                    >
                    <img :src="info.icon" class="imgIcon" v-else-if="info.icon" />
                    <img src="/static/template_icon_blank.png" class="imgIcon" v-else />
                    <div class="tsMiddle">
                      <div class="tsTop">
                        <div class="tsname">
                          {{ info.name || info.latest_published_version.title }}
                        </div>
                        <div class="tstag test" v-if="info.tag && info.tag.includes('test')">
                          test
                        </div>
                        <div class="tstag test" v-else-if="info.tag && info.tag.includes('beta')">
                          Beta
                        </div>
                        <div class="tstag last" v-if="info.calc_type && info.calc_type == 'NEWEST'">
                          {{ $t('Temlate_simple_last') }}
                        </div>
                        <div
                          class="tstag most"
                          v-else-if="info.calc_type && info.calc_type == 'MAXUSED'"
                        >
                          {{ $t('Temlate_simple_most') }}
                        </div>
                        <div class="tstag lock" v-if="info.is_locked">
                          {{ $t('Upgrade_icon_text') }}
                        </div>
                      </div>
                      <div
                        class="keyWords"
                        v-if="
                          info.custom_type === 'community' &&
                          info.latest_published_version.description
                        "
                      >
                        <div class="wordItem">{{ info.latest_published_version.description }}</div>
                      </div>
                      <div class="keyWords" v-if="info.keywords && info.keywords.length > 0">
                        <div class="wordItem" v-for="w in info.keywords">{{ w }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="templateChecked"></div>
                </div>
              </div> -->
              <!-- <div class="tsLine"></div> -->
              <!-- </template> -->
            </div>

            <!-- <div class="tsTitle" style="margin-bottom: 0px">{{ $t('Custom_template_tag') }}</div>
            <div class="cseTxt" style="margin-bottom: 12px">{{ $t('Custom_template_subtag') }}</div>
            <div class="tsList">
              <div
                class="tsItem"
                v-for="info in defineTemplateList"
                @click="doSelectTemplate(info)"
                :class="[summaryType == info.id ? 'active' : '', 'template_item_' + info.id]"
              >
                <div class="flex-middle">
                  <img src="/static/template_icon_customize.png" class="imgIcon" />
                  <div class="tsMiddle">
                    <div class="tsTop">
                      <div class="tsname">{{ info.name }}</div>
                      <div class="tstag last" v-if="info.calc_type && info.calc_type == 'NEWEST'">
                        {{ $t('Temlate_simple_last') }}
                      </div>
                      <div
                        class="tstag most"
                        v-else-if="info.calc_type && info.calc_type == 'MAXUSED'"
                      >
                        {{ $t('Temlate_simple_most') }}
                      </div>
                      <div class="tstag lock" v-if="info.is_locked">
                        {{ $t('Upgrade_icon_text') }}
                      </div>
                    </div>
                    <div class="keyWords" v-if="info.keywords && info.keywords.length > 0">
                      <div class="wordItem" v-for="w in info.keywords">{{ w }}</div>
                    </div>
                  </div>
                </div>
                <div class="templateChecked"></div>
              </div>
              <div class="tsItem" @click="addDefine()">
                <img src="/static/template_icon_create.png" class="imgIcon" />
                <div class="tsMiddle cseTxt">{{ $t('Custom_template_item_create') }}</div>
                <div></div>
              </div>
            </div> -->
          </VuePerfectScrollbar>
          <!-- 右侧 -->
          <div
            ref="mdBox"
            :class="['infoCont_right', 'pt-[5px]', isLoading ? 'loading-center' : '']"
          >
            <!-- <Loading :status="isLoading" v-if="isLoading" /> -->
            <Loading :status="isLoading" v-if="isLoading" />
            <div v-else class="grid grid-cols-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-2">
              <CardBox
                v-for="(item, index) in categoryList"
                class="!h-[141px] cursor-pointer"
                :class="{ '!h-[120px]': item.custom_type === 'custom' }"
                :data="item.custom_type === 'community' ? item[translateKey] : item"
                :index="index"
                :isActive="
                  item.custom_type === 'system'
                    ? summaryType === item.type
                    : item.custom_type === 'community'
                      ? summaryType === item[translateKey].id
                      : summaryType === item.id
                "
                :key="index"
                @handleClick="doSelectTemplate('overallDialog', item, index)"
                @expand="handleExpand(item, index)"
              ></CardBox>
            </div>
            <!-- <div class="nameTop" style="margin-top: 10px">
              <span
                class="material-symbols-rounded img !text-[48px] h-auto"
                :style="{
                  color: formatColor(selectTemplateInfo.latest_published_version.icon_color),
                }"
                v-if="selectTemplateInfo.custom_type === 'community'"
                >{{ formatIcon(selectTemplateInfo.latest_published_version.icon) }}</span
              >
              <img :src="selectTemplateInfo.icon" class="img" v-else-if="selectTemplateInfo.icon" />
              <img
                src="/static/template_icon_customize.png"
                class="img"
                v-else-if="selectTemplateInfo.id"
              />
              <img src="/static/template_icon_blank.png" class="img" v-else />
              <div class="tempInfo">
                <div class="flex-between">
                  <div class="tempName" v-if="selectTemplateInfo.custom_type === 'community'">
                    {{ selectTemplateInfo.latest_published_version.title }}
                  </div>
                  <div class="tempName" v-else>{{ selectTemplateInfo.name }}</div>
                  <div
                    class="commonBtn gray1"
                    style="min-width: 80px"
                    @click.stop="addDefine(selectTemplateInfo)"
                    v-if="selectTemplateInfo.custom_type === 'custom'"
                  >
                    {{ $t('Custom_template_item_edit') }}
                  </div>
                </div>
                <div
                  class="keyWords"
                  v-if="selectTemplateInfo.keywords && selectTemplateInfo.keywords.length > 0"
                >
                  <div class="wordItem" v-for="w in selectTemplateInfo.keywords">{{ w }}</div>
                </div>
                <div class="keyWords" v-else-if="selectTemplateInfo.custom_type === 'community'">
                  <div class="wordItem">
                    {{ selectTemplateInfo.category }}｜{{ selectTemplateInfo.usage_count }}｜{{
                      selectTemplateInfo.author_name
                    }}
                  </div>
                </div>
              </div>
            </div>
            <template v-if="!selectTemplateInfo.id">
              <div class="tempDesc">{{ selectTemplateInfo.desc }}</div>
              <div class="mdBox">
                <mavon-editor
                  style="min-height: 0px; border: none"
                  v-model="mdData"
                  placeholder="输入内容"
                  :boxShadow="false"
                  :subfield="false"
                  :toolbarsFlag="false"
                  fontSize="16px"
                  :tabSize="8"
                  previewBackground="transparent"
                  defaultOpen="preview"
                />
              </div>
            </template>
            <template v-if="selectTemplateInfo.custom_type === 'community'">
              <div class="tempDesc">
                {{ selectTemplateInfo.latest_published_version.description }}
              </div>
              <div class="mdBox">
                <mavon-editor
                  style="min-height: 0px; border: none"
                  v-model="mdData"
                  placeholder="输入内容"
                  :boxShadow="false"
                  :subfield="false"
                  :toolbarsFlag="false"
                  fontSize="16px"
                  :tabSize="8"
                  previewBackground="transparent"
                  defaultOpen="preview"
                />
              </div>
            </template>
            <template v-else>
              <div class="tsLine" style="margin-top: 20px"></div>
              <div class="definePrompt">{{ $t('Custom_template_item_prompt') }}</div>
              <div
                class="mdBox defineDescMd"
                v-html="getDefineStr(selectTemplateInfo.content)"
              ></div>
            </template> -->
          </div>
        </div>
        <!-- 底部 -->
        <div class="infoBottom">
          <div class="flex-middle selectBoxes">
            <el-popover
              popper-class="elPopoverClass"
              placement="right-end"
              width="320"
              trigger="click"
              @hide="hideModelAi()"
            >
              <VuePerfectScrollbar class="moreLangList modelai">
                <div
                  class="moreItem"
                  :class="{ active: transAi == info.id, last: index === transAiList.length - 1 }"
                  @click="doSelectAi(info.id, true)"
                  v-for="(info, index) in transAiList"
                >
                  <div>
                    <div class="flex-middle">
                      {{ info.name }}
                      <div class="beta" v-if="info.isBeta">
                        {{ $t('Filedetail_submit_transcription_distinguish_speaker_tip') }}
                      </div>
                      <div class="beta" v-if="info.is_inner">Inner</div>
                    </div>
                    <div class="model_desc">{{ info.des }}</div>
                  </div>
                  <div class="iconfont icon-Checkmark"></div>
                </div>
              </VuePerfectScrollbar>
              <template v-slot:reference>
                <div class="selectBox">
                  <div class="flex">
                    <div class="sIcon iconfont icon-template_icon_ai_model"></div>
                    <div>
                      <div class="n">{{ $t('Summary_template_model_title') }}</div>
                      <div class="s">{{ transAiInfo.name }}</div>
                    </div>
                  </div>
                  <div class="flex-middle" style="margin-left: 10px">
                    <div class="redDian" v-if="transAiList[0].isred"></div>
                    <div
                      class="nextIcon iconfont icon-sidebar_clsoure"
                      style="margin-left: 0px"
                    ></div>
                  </div>
                </div>
              </template>
            </el-popover>

            <el-popover
              style="flex: 1"
              popper-class="elPopoverClass"
              placement="right-end"
              width="260"
              trigger="click"
            >
              <VuePerfectScrollbar class="moreLangList">
                <div
                  class="moreItem"
                  :class="{ active: selectLang == lng.id, highMoreItem: showAutoTips(lng) }"
                  @click="doSelectLang(lng.id, true)"
                  v-for="lng in langList"
                  v-if="enableSummaryAuto || (pageFlag != 2 && lng.id != 'auto') || pageFlag == 2"
                >
                  <div>
                    {{ lng.name }}
                    <div class="desc" v-if="showAutoTips(lng)">
                      {{ $t('Filedetail_resubmit_summary_language_autotips') }}
                    </div>
                  </div>
                  <div class="iconfont icon-Checkmark"></div>
                </div>
              </VuePerfectScrollbar>
              <template v-slot:reference>
                <div class="selectBox relative">
                  <div class="transSummaryPop" v-if="pageFlag == 2 && showTipPop">
                    {{ $t('Filedetail_submit_summary_content') }}
                    <div class="closeBtn" @click="showTipPop = false">
                      <div class="iconfont icon-icon_delete_upload_file"></div>
                    </div>
                  </div>
                  <div class="flex">
                    <div class="sIcon iconfont icon-template_icon_language"></div>
                    <div>
                      <div class="n">
                        {{ $t('Filedetail_submit_transcription_language_title') }}
                      </div>
                      <div class="s">{{ selectLangInfo.name }}</div>
                    </div>
                  </div>
                  <div class="nextIcon iconfont icon-sidebar_clsoure"></div>
                </div>
              </template>
            </el-popover>

            <div class="selectBox nohover betaBox" v-if="pageFlag != 2">
              <div class="flex" style="margin-right: 15px; align-items: center">
                <div class="sIcon iconfont icon-template_icon_speaker_labels"></div>
                <div>
                  <div class="s">
                    {{ $t('Filedetail_submit_transcription_distinguish_speaker') }}
                  </div>
                  <div class="noSupportBox" v-if="fileInfo.duration > maxTimes">
                    <div class="transSummaryPop speaker" v-if="showLimitPop">
                      {{ $t('Speaker_limit_3hour') }}
                      <div class="closeBtn" @click="showLimitPop = false">
                        <div class="iconfont icon-icon_delete_upload_file"></div>
                      </div>
                    </div>
                    {{ $t('Speaker_limit_nosupported') }}
                    <div
                      class="iconfont tipicon icon-a-Info2"
                      @click.stop="showLimitPop = true"
                    ></div>
                  </div>
                  <div class="beta">
                    {{ $t('Filedetail_submit_transcription_distinguish_speaker_tip') }}
                  </div>
                </div>
              </div>
              <el-switch
                v-model="speakerStatus"
                :width="36"
                active-color="#1F1F1F"
                inactive-color="#D0D5DD"
                @change="changeSpeakerStatus"
              ></el-switch>
            </div>
          </div>
          <div class="commonBtn" @click="reTran()" style="width: 240px">
            <div class="cseTxt">
              {{ $t('Filedetail_submit_generate_now') }}
            </div>
          </div>
        </div>
      </div>
      <!-- 个人模板create/edit/use弹窗 -->
      <!-- <MyDialog
        :title="$t('Custom_template_edit_title')"
        width="580px"
        :modal="false"
        :toBody="true"
        :visible="defineStatus"
        @close="defineStatus = false"
      >
        <template v-slot:content>
          <div>
            <div class="defineForm">
              <div class="defineTitle">{{ $t('Custom_template_edit_name') }}</div>
              <input
                type="text"
                v-model="formInfo.name"
                class="defineIpt"
                @input="changeIpt('name')"
                :placeholder="$t('Custom_template_edit_name_hint')"
              />
              <div class="formError" :class="{ visible: formError.name && formError.name.status }">
                {{ formError.name ? formError.name.msg : '' }}
              </div>
              <div class="defineTitle">
                {{ $t('Custom_template_edit_prompt') }}
                <el-popover
                  popper-class="elPopoverClass elPopoverHelpClass"
                  :disabled="!defineStatus"
                  placement="right-start"
                  width="510"
                  trigger="click"
                >
                  <iframe
                    src="/common.html#/templates_help?from=web"
                    frameborder="0"
                    style="width: 100%; height: 70vh; border-radius: 13px"
                  ></iframe>
                  <div
                    class="iconfont icon-Close"
                    style="
                      position: absolute;
                      right: 8px;
                      top: 5px;
                      z-index: 2;
                      color: rgba(60, 60, 67, 0.6);
                      cursor: pointer;
                    "
                    @click="domClick()"
                  ></div>
                  <template v-slot:reference>
                    <div class="help">
                      <span class="iconfont icon-help-circle"></span>
                      <span>{{ $t('Custom_template_edit_prompt_sub') }}</span>
                    </div>
                  </template>
                </el-popover>
              </div>
              <textarea
                v-model="formInfo.content"
                class="defineIpt textarea"
                @input="changeIpt('content')"
                :placeholder="$t('Custom_template_edit_prompt_hint')"
              ></textarea>
              <div
                class="formError"
                :class="{ visible: formError.content && formError.content.status }"
              >
                {{ formError.content ? formError.content.msg : '' }}
              </div>
              <div class="commonBtn" @click="saveDefine()" v-if="!formInfo.id">
                {{ $t('Custom_template_edit_btn_create') }}
              </div>
              <div class="btns" v-else>
                <div class="commonBtn del" @click="deleteDefine()">
                  {{ $t('Custom_template_edit_btn_delete') }}
                </div>
                <div class="commonBtn" @click="saveDefine()">
                  {{ $t('Custom_template_edit_btn_save') }}
                </div>
              </div>
              <Loading :status="isLoading" />
            </div>
          </div>
        </template>
      </MyDialog> -->
    </slot>
  </el-dialog>
</template>

<script>
import langList from '@/data/trans_language.js';
import sysLangList from '@/data/sys_language.js';
import { mapBrowserLanguageToId } from '@/data/browser_language_map.js';
import VuePerfectScrollbar from 'vue-perfect-scrollbar';
import MyDialog from '../../common/MyDialog.vue';
import { markdownSpeakerEditFormat } from '@/common/markdown';
import { storage } from '@/util/storage';
import CardBox from './CardBox.vue';
import { mavonEditor } from 'mavon-editor';
import 'mavon-editor/dist/css/index.css';
import { findIconCodeToName, convertToHexColor } from '@/util/material-icons-collection.js';

export default {
  name: 'TranSummary',
  components: { VuePerfectScrollbar, MyDialog, mavonEditor, CardBox },
  data() {
    return {
      dialogStatus: false,
      pageFlag: -1, //0:all,1:trans，2：summary
      isViewAll: false,
      fileInfo: {},
      langList: langList,
      // 一级菜单名字
      selectName: '',
      // 模版id
      summaryType: '',
      summaryDefine: false,
      summaryList: [],
      // 最近使用的模版列表
      recentUseList: [],
      // 自定义模版
      customList: [],
      simpleList: [],
      selectLang: 'en',
      defineStatus: false,
      upgradeStatus: false,
      speakerStatus: false,
      formInfo: {
        name: '',
        content: '',
      },
      formError: {},
      defineTemplateList: [],
      tipkey: '',
      mdData: '',
      transAi: 'auto',
      transAiList: [
        // { id: 'auto', name: 'Auto', isred: false },
        // { id: 'openai', name: this.$t('Summary_template_model_name_4o') },
        // { id: 'claude-3.7', name: 'Claude 3.7' },
        // { id: 'o3-mini', name: 'o3-mini', isbeta: true },
      ],
      maxTimes: 3 * 60 * 60 * 1000,
      showTipPop: false,
      lastUseList: [], //之前点过NEW的
      showLimitPop: false,
      osLang: 'en',
      firstViewAll: false,
      tranConfigInfo: {},
      wholeSetInfo: { language: '', baseLanguage: '' },
      enableSummaryAuto: false,
      // hasAuto: false,
      recentCategory: 'Recently Used',
      customCategory: 'Customize',
      categoryList: [],
      // translateKey: 'orignal',
      translateKey: '',
      statusMapColor: {
        try: 'orange',
        lastUse: 'blue',
      },
      statusMapLanguage: {
        try: 'try_text',
        lastUse: 'Temlate_simple_last',
      },
      tryData: {},
    };
  },
  computed: {
    initSmall() {
      return this.pageFlag === 0 && !this.isViewAll;
    },
    transAiInfo() {
      let info = this.transAiList.find((item) => item.id == this.transAi);
      if (!info) {
        info = this.transAiList[0];
        this.transAi = info?.id;
      }
      return info;
    },
    selectLangInfo() {
      let info = this.langList.find((item) => item.id == this.selectLang);
      if (!info) {
        info = this.langList[1];
        this.selectLang = info.id;
      }
      return info;
    },
    selectTemplateInfo() {
      // console.log('selectTemplateInfo-0602:');
      let info = {},
        md = '';
      let setInfo = () => {
        for (let i = 0; i < this.summaryList.length; i++) {
          let list = this.summaryList[i].data;
          // info = list.find((item) => item.type == this.summaryType);
          // 区分 custom_type 判断 type 或 id
          info = list.find((item) => {
            if (item.custom_type === 'community') {
              return item[this.translateKey].id == this.summaryType;
            } else {
              return item.type == this.summaryType;
            }
          });
          if (info) {
            this.selectName = info.category;
            if (info.custom_type === 'community') {
              md = markdownSpeakerEditFormat(info[this.translateKey].content.replace(/\\n/g, '\n'));
            } else {
              md = markdownSpeakerEditFormat(info.pre_markdown.replace(/\\n/g, '\n'));
            }
            break;
          }
        }
      };
      setInfo();
      if (!info) {
        // info = this.defineTemplateList.find((item) => item.id == this.summaryType);
        info = this.customList.find((item) => item.id == this.summaryType);
        if (!info) {
          info = {};
          this.selectName = 'General';
          this.summaryType = 'MEETING';
          this.summaryDefine = false;
          setInfo();
        }
      }
      this.mdData = md;
      return info;
    },
  },
  watch: {
    dialogStatus: {
      handler(newVal) {
        if (newVal) {
          this.getTryItTemplate();
          this.translateKey =
            storage.get('translateKey', this.$root.userInfo.id)?.value || 'translated';
        } else {
          this.categoryList = [];
        }
      },
      immediate: true,
    },
  },
  mounted() {
    //记录点过的new的模板。推荐的new模板，点过一次就不显示new了
    let plaudtranlastuse = localStorage.getItem('plaudtranlastuse');
    if (plaudtranlastuse && plaudtranlastuse != '') {
      this.lastUseList = plaudtranlastuse.split(',');
    }
    //view all的小红点，仅显示一次
    let plaudfirstts = localStorage.getItem('plaudfirstts');
    if (!plaudfirstts) {
      this.firstViewAll = true;
    }
    let osLang = 'en',
      langInfo = sysLangList.find((item) => item.id == this.$i18n.locale);
    if (langInfo) {
      osLang = langInfo.tempid;
    }
    this.osLang = osLang;
    this.getTemplateList();
    // this.getDefineList();

    this.reqGetInfo('/user/me/settings').then((data) => {
      let { language, language_is_default } = data;
      if (language_is_default === 1) {
        //用户自定义
        this.wholeSetInfo.language = language;
      } else {
        //兜底
        this.wholeSetInfo.baseLanguage = language;
      }
    });
    this.reqGetInfo(`/summary/models/config?language=${osLang}`).then((data) => {
      data.map((item) => (item.id = item.model_name));
      this.transAiList = data;
    });
    this.transAi = localStorage.getItem('transAi');
    this.$nextTick(() => {
      this.reqGetInfo(`/config/init?platform=web&version=1.1.1`).then((data) => {
        this.enableSummaryAuto = !!data.enable_auto_language;
      });
    });
  },
  beforeDestroy() {},
  methods: {
    // 获取try 模版
    getTryItTemplate() {
      let tryObj = JSON.parse(storage.get('tryItTemplate', this.$root.userInfo.id)?.value || '{}');
      let summaryType = '';
      if (tryObj && Object.keys(tryObj).length > 0) {
        if (tryObj.custom_type === 'community') {
          summaryType = tryObj.orignal.id;
        } else {
          summaryType = tryObj.type;
        }
        this.tryData = {
          ...tryObj,
          custom_id: 'tryIt',
          summaryType,
        };
        this.selectName = this.recentCategory;
      }
      // console.log('getTryItTemplate-0606:', this.tryData);
    },
    getCategoryList(category) {
      if (category === this.recentCategory) {
        this.categoryList = this.recentUseList;
      } else if (category === this.customCategory) {
        this.categoryList = this.customList;
      } else {
        this.categoryList = this.summaryList.find((item) => item.category == category)?.data || [];
      }
    },
    changeCategory(category) {
      // console.log('changeCategory-0530:', category);
      this.selectName = category;
      this.getCategoryList(category);
      this.initalScroll();
    },
    getImagePath(icon) {
      try {
        return require(`../../../../public/static/${icon}.svg`);
      } catch (e) {
        console.error(`Image not found: ../../../../public/static/${icon}.svg`, e);
        return '';
      }
    },
    // handleTest() {
    //   console.log('handleTest');
    //   this.dialogStatus = false;
    //   this.$emit('preview');
    // },
    // 处理自定义模版数据
    processCustomTemplate(list) {
      return {
        ...list,
        custom_type: 'custom',
      };
    },
    // 处理最近使用模版数据
    processRecentlyTemplateData(list, isFirst = false) {
      let { type, template } = list;
      let baseData = {};
      if (type === 'community') {
        const {
          latest_published_version,
          translated_published_version,
          author_name,
          usage_count,
          id,
          is_locked,
        } = template;
        const baseTemplateData = {
          custom_type: 'community',
          id: translated_published_version.id,
          category:
            this.translateKey === 'translated'
              ? translated_published_version.trans_category
              : latest_published_version.category,
          authorName: author_name,
          usageCount: usage_count,
        };
        baseData = {
          custom_type: 'community',
          ...baseTemplateData,
          orignal: {
            ...baseTemplateData,
            iconColor: convertToHexColor(latest_published_version.icon_color),
            iconName: findIconCodeToName(latest_published_version.icon),
            title: latest_published_version.title,
            description: latest_published_version.description,
            content: latest_published_version.content || '',
            id: latest_published_version.id || '',
            isLocked: is_locked === 1 || false,
          },
          translated: {
            // ...item,
            ...baseTemplateData,
            iconColor: convertToHexColor(translated_published_version.icon_color),
            iconName: findIconCodeToName(translated_published_version.icon),
            title: translated_published_version.title,
            description: translated_published_version.description,
            content: translated_published_version.content || '',
            id: translated_published_version.id || '',
            isLocked: is_locked === 1 || false,
          },
        };
      } else if (type === 'official') {
        baseData = {
          ...template,
          custom_type: 'system',
          isLocked: template.is_locked === 1 || false,
        };
        // 只为第一条数据添加last use badge
      } else {
        baseData = {
          ...template,
          custom_type: 'custom',
          isLocked: template.is_locked === 1 || false,
        };
      }
      return baseData;
    },
    // 处理官方+社区模版
    processTemplateData(list) {
      const { category, category_id, icon, data, community_templates } = list;

      // 处理系统模板数据
      const processSystemTemplate = (item) => ({
        ...item,
        // id: item.name,
        custom_type: 'system',
        isLocked: item.is_locked === 1 || false,
      });

      // 处理社区模板数据
      const processCommunityTemplate = (item) => {
        const {
          latest_published_version,
          translated_published_version,
          author_name,
          usage_count,
          id,
          template_id,
          is_locked,
        } = item;
        const baseTemplateData = {
          custom_type: 'community',
          category,
          category_id,
          category_icon: icon,
          authorName: author_name,
          usageCount: usage_count,
        };

        return {
          custom_type: 'community',
          id: translated_published_version.id,
          category,
          orignal: {
            // ...item,
            ...baseTemplateData,
            iconColor: convertToHexColor(latest_published_version.icon_color),
            iconName: findIconCodeToName(latest_published_version.icon),
            title: latest_published_version.title,
            description: latest_published_version.description,
            content: latest_published_version.content || '',
            id: latest_published_version.id || '',
            isLocked: is_locked === 1 || false,
          },
          translated: {
            // ...item,
            ...baseTemplateData,
            iconColor: convertToHexColor(translated_published_version.icon_color),
            iconName: findIconCodeToName(translated_published_version.icon),
            title: translated_published_version.title,
            description: translated_published_version.description,
            content: translated_published_version.content || '',
            id: translated_published_version.id || '',
            isLocked: is_locked === 1 || false,
          },
        };
      };

      return {
        category,
        category_id,
        icon,
        data: [
          ...data.map(processSystemTemplate),
          ...community_templates.map(processCommunityTemplate),
        ],
      };
    },
    getTemplateList(changeFlag = true) {
      let myLang = this.selectLang;
      //如果总结的语言选择的auto，那就取上一次本地存储的转写语言
      // if (myLang == 'auto') {
      //   let plaudtranlangStr = localStorage.getItem('plaudtranlang');
      //   if (plaudtranlangStr && this.langList.some((item) => item.id == plaudtranlangStr)) {
      //     myLang = plaudtranlangStr;
      //   }
      // }
      if (myLang == 'auto') {
        myLang = this.getlanguage(true);
      }
      this.categoryList = [];
      this.isLoading = true;
      // this.reqGetInfo(
      //   `/ai/chatllm/templates?all_templates=1&language_os=${this.osLang}&language_trans=${myLang}`,
      // ).then((data) => {
      //   this.summaryList = data.templates;
      //   this.isLoading = false;
      // });
      this.reqGetInfo(
        `/summary/chatllm/templates?has_recently_use=true&has_custom=true&language_os=${this.osLang}&language_trans=${myLang}`,
      ).then((data) => {
        // 模版分类列表，每个分类包含系统模板和社区模板
        let summaryList = data.templates.map((list) => this.processTemplateData(list));
        this.summaryList = summaryList;
        // 最近使用模版列表
        let recentUseList = (data.Recently || []).map((list, index) =>
          this.processRecentlyTemplateData(list),
        );
        if (recentUseList.length > 0) {
          let { custom_type } = recentUseList[0];
          if (custom_type === 'community') {
            recentUseList[0].orignal.badgeStatus = this.statusMapColor.lastUse || 'blue';
            recentUseList[0].orignal.badgeText =
              this.statusMapLanguage.lastUse || 'Temlate_simple_last';
            recentUseList[0].translated.badgeStatus = this.statusMapColor.lastUse || 'blue';
            recentUseList[0].translated.badgeText =
              this.statusMapLanguage.lastUse || 'Temlate_simple_last';
          } else {
            recentUseList[0].badgeStatus = this.statusMapColor.lastUse || 'blue';
            recentUseList[0].badgeText = this.statusMapLanguage.lastUse || 'Temlate_simple_last';
          }
        }
        this.recentUseList = recentUseList;

        // 自定义模版列表
        this.customList = (data.Custom || []).map((list) => this.processCustomTemplate(list));
        this.customList.push({
          custom_type: 'custom',
          id: 'create',
          name: this.$t('Folder_create_button'),
        });

        // console.log('this.recentUseList-123:', this.recentUseList);

        // 处理try模版数据,放到recentList中
        let { custom_type } = this.tryData;
        if (custom_type === 'community') {
          if (this.tryData?.orignal && typeof this.tryData.orignal === 'object') {
            Object.assign(this.tryData.orignal, {
              badgeStatus: this.statusMapColor.try,
              badgeText: this.statusMapLanguage.try,
              custom_type: 'community',
            });
          }
          if (this.tryData?.translated && typeof this.tryData.translated === 'object') {
            Object.assign(this.tryData.translated, {
              badgeStatus: this.statusMapColor.try,
              badgeText: this.statusMapLanguage.try,
              custom_type: 'community',
            });
          }
        } else if (custom_type === 'system') {
          Object.assign(this.tryData, {
            badgeStatus: this.statusMapColor.try,
            badgeText: this.statusMapLanguage.try,
            custom_type: 'system',
          });
        }
        if (this.tryData && Object.keys(this.tryData).length > 0) {
          this.recentUseList.unshift(this.tryData);
        }
        // console.log('recentUseList-0609:', this.recentUseList);

        // 去重
        this.recentUseList = this.recentUseList.filter((item, index, arr) => {
          const key = item?.id ?? item?.type;
          return arr.findIndex((i) => (i?.id ?? i?.type) === key) === index;
        });

        // 右侧分类列表
        this.dealTemplateData(changeFlag);
        // console.log('summaryList-0530:', this.summaryList);
        // console.log('recentUseList-0530:', this.recentUseList);
        // console.log('customList-0530:', this.customList);
        this.isLoading = false;
      });
    },
    goToView() {
      if (this.summaryType) {
        this.$nextTick(() => {
          let summaryDom = this.$el.querySelector('.template_item_' + this.summaryType);
          if (summaryDom) {
            this.$refs.contLeft.$el.scrollTop = summaryDom.offsetTop - 160;
          }
        });
      }
    },
    getLastTemplate() {
      if (this.pageFlag === 0) {
        this.isLoading = true;
      }
      this.reqGetInfo(`/ai/chatllm/templates-simple?language_os=${this.osLang}`).then((data) => {
        this.isLoading = false;
        let key = this.$parent.info && this.$parent.info.scene === 7 ? 'CALL' : 'NOTE';
        if (data.templates) {
          let mySimpleList = data.templates[key];
          if (data.templates.AUTO && data.templates.AUTO.length > 0) {
            //auto里面是new模板，如果new不在推荐里面，就放在数组最前面
            data.templates.AUTO.forEach((item) => {
              if (
                !mySimpleList.some((info) => info.type == item.type) &&
                !this.lastUseList.some((u) => u == item.type)
              ) {
                mySimpleList.unshift(item);
              }
            });
          }
          // console.log('mySimpleList-0606:', mySimpleList);
          this.simpleList = mySimpleList.map((item) => {
            if (item.type_type === 'community') {
              return {
                ...item,
                custom_type: 'community',
                type_type: 'community',
                id: item.id,
                category: this.translateKey === 'translated' ? item.to_category : item.category,
                orignal: {
                  custom_type: 'community',
                  id: item.id || '',
                  iconColor: convertToHexColor(item.icon_color),
                  iconName: findIconCodeToName(item.icon),
                  name: item.name,
                  title: item.name,
                  description: item.description,
                  content: item.content || '',
                  template_id: item.template_id || '',
                  isLocked: item.is_locked === 1 || false,
                  category: this.translateKey === 'translated' ? item.to_category : item.category,
                },
                translated: {
                  custom_type: 'community',
                  id: item.id || '',
                  iconColor: convertToHexColor(item.icon_color),
                  iconName: findIconCodeToName(item.icon),
                  name: item.to_name,
                  title: item.to_name,
                  description: item.to_description,
                  content: item.to_content || '',
                  template_id: item.template_id || '',
                  isLocked: item.is_locked === 1 || false,
                  category: this.translateKey === 'translated' ? item.to_category : item.category,
                },
              };
            } else {
              return item;
            }
          });

          if (this.simpleList.length > 0) {
            let currentLastUseInfo = {};
            let lastUseInfo = this.simpleList.find((info) => info.calc_type == 'NEWEST');
            //如果没有最近使用，就默认取第一个
            if (!lastUseInfo) {
              currentLastUseInfo = this.simpleList[0];
            } else {
              currentLastUseInfo = lastUseInfo;
            }
            let { type, id, category } = currentLastUseInfo;
            if (this.tryData && Object.keys(this.tryData).length > 0) {
              this.selectName = this.recentCategory;
            } else {
              this.selectName = lastUseInfo ? this.recentCategory : category;
            }

            if (type) {
              this.summaryType = type;
              this.summaryDefine = false;
            } else {
              this.summaryType = id;
              this.summaryDefine = true;
            }
          }
          // console.log('this.summaryType-221366:', this.summaryType);
          // console.log('this.summaryDefine-2213:', this.summaryDefine);
          // console.log('this.selectName:', this.selectName);

          if (this.pageFlag != 0) {
            this.getTemplateList();
            this.goToView();
          }
          if (this.pageFlag == 2) {
            //总结自动弹出的提示框，显示一次
            let tstippopstatus = localStorage.getItem('tstippopstatus');
            if (!tstippopstatus && !this.enableSummaryAuto) {
              this.showTipPop = true;
              localStorage.setItem('tstippopstatus', true);
            }
          } else {
            //如果不是会员或者大于maxTime,设置为FALSE
            if (this.fileInfo.duration > this.maxTimes) {
              this.showLimitPop = true;
              this.speakerStatus = false;
            } else {
              if (this.tranConfigInfo.diarization === 1) {
                this.speakerStatus = true;
              } else {
                let plaudtranspeaker = localStorage.getItem('plaudtranspeaker');
                if (plaudtranspeaker && plaudtranspeaker == 'true') {
                  this.speakerStatus = true;
                } else {
                  this.speakerStatus = false;
                }
              }
            }
          }
        } else {
          this.simpleList = [];
        }
        if (this.tryData && Object.keys(this.tryData).length > 0 && this.simpleList.length > 0) {
          console.log('this.tryData-0609:', this.tryData);
          console.log('this.simpleList-0609:', this.simpleList);
          /**
           * 判断tryData是否已经在simpleList中，
           * 1.如果已经存在，那么不添加tryData, 把相同的这一项放到第一个位置
           * 2.如果不存在，那么添加tryData, 并放到第一个位置
           */
          let isExist = this.simpleList.some((item) => {
            return item.id === this.tryData?.summaryType || item.type === this.tryData?.summaryType;
            // if (item.custom_type === 'community') {
            //   return item.id === this.tryData?.id;
            // } else {
            //   return item.type === this.tryData?.type;
            // }
          });
          console.log('isExist-0609:', isExist);
          if (isExist) {
            const idx = this.simpleList.findIndex(
              (item) => (item.id ?? item.type) === this.tryData.summaryType,
            );
            if (idx > 0) {
              const existItem = this.simpleList.splice(idx, 1)[0];
              console.log('existItem-0609:', existItem);

              this.simpleList.unshift(existItem);
              this.summaryType = this.tryData.id ?? this.tryData.type;
              this.tryData = {};
              storage.remove('tryItTemplate', this.$root.userInfo.id);
            }
          } else {
            let { custom_type } = this.tryData;
            if (custom_type === 'community') {
              let { title, description, id } = this.tryData[this.translateKey];
              this.simpleList.unshift({
                ...this.tryData,
                name: title,
                description,
                custom_id: 'tryIt',
                id,
              });
              this.summaryType = id;
            } else if (custom_type === 'system') {
              let { keywords, type } = this.tryData;
              this.simpleList.unshift({
                ...this.tryData,
                keywords,
                custom_id: 'tryIt',
              });
              this.summaryType = type;
            }
          }

          this.summaryDefine = false;
        }
        this.dealTemplateData();
      });
    },
    // getDefineList() {
    //   this.reqGetInfo('/ai/customtemplates').then((data) => {
    //     this.defineTemplateList = data.map((item) => ({
    //       ...item,
    //       custom_type: 'custom',
    //     }));
    //   });
    // },
    getDefineStr(str) {
      if (!str) return str;
      return str.replace(/\n/g, '<br/>');
    },
    doSelectLang(id, close = false) {
      this.selectLang = id;
      //只有单独总结才有auto，不存
      // if (id != 'auto') {
      //   localStorage.setItem('plaudtranlang', id);
      // }
      // 对于auto，纯总结不用存在本地，其他要存在本地
      if (!(this.pageFlag === 2 && id === 'auto')) {
        localStorage.setItem('plaudtranlang', id);
      }
      if (!this.initSmall) {
        this.getTemplateList(false);
      }
      if (close) {
        this.domClick();
      }
    },
    doViewAll() {
      localStorage.setItem('plaudfirstts', true);
      this.firstViewAll = false;
      this.isViewAll = true;
      this.getTemplateList();
      this.goToView();
    },
    doSelectAi(id, close = false) {
      this.transAi = id;
      localStorage.setItem('transAi', id);
      if (close) {
        this.domClick();
      }
    },
    changeSpeakerStatus(status) {
      if (status && this.fileInfo.duration > this.maxTimes) {
        setTimeout(() => {
          this.showLimitPop = true;
        }, 200);
        this.speakerStatus = false;
      }
    },
    closePop() {
      this.simpleList = [];
      this.dialogStatus = false;
    },
    handlerVisible(status, flag, info) {
      this.pageFlag = flag;
      this.fileInfo = info;
      this.dialogStatus = status;
      this.speakerStatus = false;
      this.isViewAll = false;
      this.showTipPop = false;
      this.showLimitPop = false;
      this.upgradeStatus = false;
      this.summaryType = '';
      if (status) {
        let { email } = this.$root.userInfo;
        let redStatus = storage.get('modelAiShow', email);
        if (!redStatus) {
          this.transAiList[0].isred = true;
        }
        storage.set('modelAiShow', 'true', email, 365);

        if (info.extra_data && info.extra_data.tranConfig) {
          // let {language,type_type,type,diarization,llm} = info.extra_data.tranConfig;
          this.tranConfigInfo = info.extra_data.tranConfig;
        }
        if (this.tranConfigInfo.llm) {
          this.transAi = this.tranConfigInfo.llm;
        }
        this.selectLang = this.getlanguage();
        // this.selectName = info.category;
        // 如果这里出现的是Auto，那应该在列表中让Auto出现(针对非灰度场景的兼容处理)
        // if (this.selectLang == 'auto') {
        //   this.hasAuto = true;
        // }

        // console.log('计算转写语言前');
        // plaudtranlangStr =
        //   this.wholeSetInfo.language ||
        //   this.tranConfigInfo.language ||
        //   localStorage.getItem('plaudtranlang');
        // console.log(plaudtranlangStr, 'plaudtranlangStr');
        // if (plaudtranlangStr && this.langList.some((item) => item.id == plaudtranlangStr)) {
        //   if (plaudtranlangStr == 'auto') {
        //     plaudtranlangStr = this.wholeSetInfo.baseLanguage || 'en';
        //   }
        //   this.selectLang = plaudtranlangStr;
        // } else {
        //   const language = navigator.language || navigator.userLanguage;
        //   let info = this.langList.find((item) => item.id == language.toLowerCase());
        //   if (info) {
        //     this.selectLang = info.id;
        //   } else {
        //     this.selectLang = this.wholeSetInfo.baseLanguage || 'en';
        //   }
        // }
        this.getLastTemplate();
      } else {
        this.simpleList = [];
        this.pageFlag = -1;
        this.tranConfigInfo = {};
      }
      this.setGALogEvent(['web_transcribe', 'web_retranscribe', 'web_resummary'][flag]);
    },
    getlanguage(noAuto = false) {
      // console.log('before compute trans');
      let plaudtranlangStr = '';

      // 在文件详情页，发起转写总结时，
      // 1.若是重新转写总结，则默认跟随上次转写总结时选择的语言
      // 2.若是新提交转写总结
      // 若用户在偏好设置里，有设置的语种，则显示该语种。
      // 若没有设置的语种即跟随上次转写
      // 若没有上次转写即默认为系统语言
      if (this.pageFlag === 1 || this.pageFlag === 2) {
        // 重新转写总结，默认跟随上次转写总结时选择的语言
        plaudtranlangStr = this.tranConfigInfo.language;
        // console.log(`retry trans and sum, language is：${plaudtranlangStr}`);
      }
      if (!plaudtranlangStr || (noAuto && plaudtranlangStr == 'auto')) {
        // 新提交的转写总结 或 后端没存储上次的转写语言，用设置的语种，没有设置语种，用上次转写
        plaudtranlangStr = this.wholeSetInfo.language || localStorage.getItem('plaudtranlang');
        // console.log(
        //   `New transcription summary: The language of the last transcription was not returned by the backend, language is：${plaudtranlangStr}`,
        // );
      }

      if (
        !(plaudtranlangStr && this.langList.some((item) => item.id == plaudtranlangStr)) ||
        !plaudtranlangStr ||
        (noAuto && plaudtranlangStr == 'auto')
      ) {
        // 当前计算的语言不在列表内或者当前计算的语言为空，那就用系统语言
        const language = mapBrowserLanguageToId();
        let info = this.langList.find((item) => item.id == language.toLowerCase());
        if (info) {
          plaudtranlangStr = info.id;
          // console.log(`browser language is：${plaudtranlangStr}`);
        } else {
          plaudtranlangStr = this.wholeSetInfo.baseLanguage || 'en';
          // console.log(`fallback language：${plaudtranlangStr}`);
        }
      }
      return plaudtranlangStr;
    },
    hideModelAi() {
      this.transAiList[0].isred = false;
    },
    initalScroll() {
      this.$nextTick(() => {
        if (this.$refs.mdBox) {
          this.$refs.mdBox.scrollTop = 0;
        }
      });
    },
    dealTemplateData(changeFlag = true) {
      /**
       * 找到匹配的项目并移到最前面，优先匹配最近使用，其次匹配summaryList
       * 1.如果匹配到最近使用，则把左侧的类别选中最近使用，选中项移动到最前面
       * 2.如果匹配summaryList，则把左侧的类别选中summaryList对应的类别，选中项移动到类别最前面
       */
      let dealSummaryList = () => {
        // 找到匹配的项并移到第一项
        const matchedCategoryIndex = this.summaryList.findIndex((item) =>
          item.data.some((template) => template.category === this.selectName),
        );
        console.log('dealSummaryList-matchedCategoryIndex:', matchedCategoryIndex);

        if (matchedCategoryIndex !== -1) {
          const matchedCategory = this.summaryList[matchedCategoryIndex];
          // 再把data里选中的项移到data第一项
          const selectedIndex = matchedCategory.data.findIndex((item) => {
            if (item.custom_type === 'community') {
              return item[this.translateKey].id === this.summaryType;
            } else {
              return item.type === this.summaryType;
            }
          });

          if (selectedIndex > 0) {
            const selected = matchedCategory.data.splice(selectedIndex, 1)[0];
            matchedCategory.data.unshift(selected);
          }
        }
      };

      // let a = this.recentUseList.some(
      //   (item) =>
      //     item.type === this.summaryType ||
      //     item.id === this.summaryType ||
      //     item.summaryType === this.summaryType,
      // );
      // console.log('a-0610:', a);

      if (this.selectName) {
        if (this.recentUseList.length > 0) {
          if (
            this.recentUseList.some(
              (item) =>
                item.type === this.summaryType ||
                item.id === this.summaryType ||
                item.summaryType === this.summaryType,
            )
          ) {
            const rIndex = this.recentUseList.findIndex(
              (item) =>
                item.type === this.summaryType ||
                item.id === this.summaryType ||
                item.summaryType === this.summaryType,
            );
            console.log('rIndex-0609:', rIndex);
            const selected = this.recentUseList.splice(rIndex, 1)[0];
            this.recentUseList.unshift(selected);
            if (changeFlag) {
              this.selectName = this.recentCategory;
            }
          }
          if (changeFlag) {
            this.categoryList = this.recentUseList;
          } else {
            this.getCategoryList(this.selectName);
          }
        } else {
          dealSummaryList();
          if (changeFlag) {
            this.categoryList = this.summaryList.find(
              (item) => item.category == this.selectName,
            )?.data;
          } else {
            this.getCategoryList(this.selectName);
          }
        }
      }
    },
    doSelectTemplate(type, info, index = 0) {
      console.log('doSelectTemplate-0530:', info, index);
      if (info.isLocked || info[this.translateKey]?.isLocked) {
        this.$parent.showUpgradeModel(true, 2);
      } else {
        if (info.type) {
          // system
          this.summaryDefine = false;
          this.summaryType = info.type;
          if (type === 'simpleDialog') {
            this.selectName = info.category;
          }
          // initalScroll();
        } else if (info.custom_type === 'community') {
          this.summaryDefine = false;
          this.summaryType = info[this.translateKey].id;
          if (type === 'simpleDialog') {
            this.selectName = info[this.translateKey].category;
          }
          // initalScroll();
        } else {
          // custom
          if (info.id === 'create') {
            this.summaryDefine = true;
            this.summaryType = undefined;
            this.addDefine();
          } else {
            this.summaryDefine = true;
            this.summaryType = info.id;
            if (type === 'simpleDialog') {
              this.selectName = this.customCategory;
            }
          }
          // initalScroll();
        }
        if (type === 'simpleDialog') {
          this.dealTemplateData();
        }

        // console.log('this.summaryType-0609:', this.summaryType);
        // console.log('this.selectName-0609:', this.selectName);

        // console.log('this.recentUseList-doselect:', this.recentUseList);
        // 判断如果选中的this.summaryType 在this.recentUseList中，则将this.recentUseList中对应的项移到最前面，并且左侧类别选中最近使用
        // if (type === 'simpleDialog') {
        //   if (
        //     this.recentUseList.some(
        //       (item) =>
        //         item.type === this.summaryType ||
        //         item.id === this.summaryType ||
        //         item.summaryType === this.summaryType,
        //     )
        //   ) {
        //     const rIndex = this.recentUseList.findIndex(
        //       (item) =>
        //         item.type === this.summaryType ||
        //         item.id === this.summaryType ||
        //         item.summaryType === this.summaryType,
        //     );
        //     console.log('rIndex-0609:', rIndex);
        //     const selected = this.recentUseList.splice(rIndex, 1)[0];
        //     this.recentUseList.unshift(selected);
        //     this.selectName = this.recentCategory;
        //   }
        // }

        // new的模板，点过之后就不显示了
        if (info.calc_type == 'NEW' && !this.lastUseList.some((u) => u == info.type)) {
          this.lastUseList.push(info.type);
          localStorage.setItem('plaudtranlastuse', this.lastUseList.join(','));
        }
      }
    },
    // 模版会员判断
    checkTemplateMember() {
      let { custom_type } = this.selectTemplateInfo;
      let currentLocked = false;
      if (custom_type === 'community') {
        currentLocked = this.selectTemplateInfo[this.translateKey].isLocked;
      } else {
        currentLocked = this.selectTemplateInfo.isLocked;
      }
      if (currentLocked) {
        this.$parent.showUpgradeModel(true, 3);
        return false;
      }
      return true;
    },
    reTran() {
      // console.log('reTran-0618-this.selectTemplateInfo:', this.selectTemplateInfo);
      if (!this.checkTemplateMember()) {
        return false;
      }
      if (!this.summaryType) {
        this.setMessage(this.$t('Filelist_submit_notemplate'));
        return false;
      }
      if (this.speakerStatus && this.fileInfo.duration > this.maxTimes) {
        setTimeout(() => {
          this.showLimitPop = true;
        }, 200);
        return false;
      }

      // console.log('reTran-0609-this.summaryType:', this.summaryType);
      // console.log('reTran-0609-this.tryData:', this.tryData);
      // 点击生成，需要清除try模版的缓存
      // if (this.summaryType === this.tryData.summaryType) {
      this.tryData = {};
      storage.remove('tryItTemplate', this.$root.userInfo.id);
      // 把recentList中的try模版移除
      this.recentUseList = this.recentUseList.filter(
        (item) => item.summaryType !== this.summaryType,
      );
      // }
      this.dealTemplateData();
      // return false

      localStorage.setItem('plaudtranspeaker', this.speakerStatus);
      this.$emit('retran', {
        flag: this.pageFlag,
        lang: this.selectLang,
        type: this.summaryType,
        // subtype: this.summaryDefine ? 'custom' : 'system',
        subtype: this.selectTemplateInfo.custom_type,
        isSpeaker: this.speakerStatus,
        transAi: this.transAi,
      });
      const params = {
        extra_data: {
          ...this.fileInfo.extra_data,
          tranConfig: {
            language: this.selectLang,
            // type_type: this.summaryDefine ? 'custom' : 'system',
            type_type: this.selectTemplateInfo.custom_type,
            type: this.summaryType,
            diarization: this.speakerStatus ? 1 : 0,
            llm: this.transAi,
          },
        },
      };
      this.reqPatchInfo('/file/' + this.fileInfo.id, params);
      this.closePop();
    },
    addDefine(info = {}) {
      // this.formError = {};
      if (!['pro', 'backer', 'unlimited'].includes(this.$root.userStateInfo.membership_type)) {
        this.$parent.showUpgradeModel(true, 3);
        return false;
      }
      // 创建新个人模板逻辑
      // this.dialogStatus = false;
      this.defineStatus = true;
      // console.log('addDefine-0609-info:', info);
      this.$emit('createTemplate', { custom_status: 'saveAsTemplate', from: 'tranSummary' });

      // if (info.id) {
      //   for (let k in this.formInfo) {
      //     this.formInfo[k] = info[k];
      //   }
      //   this.formInfo.id = info.id;
      //   this.defineStatus = true;
      // } else {
      //   if (this.defineTemplateList.length >= 100) {
      //     this.setMessage(this.$t('Custom_template_toast_maxlimit'));
      //   } else {
      //     delete this.formInfo.id;
      //     for (let k in this.formInfo) {
      //       this.formInfo[k] = '';
      //     }
      //     this.defineStatus = true;
      //   }
      // }
    },
    changeIpt(key) {
      if (key == 'name' && this.formInfo.name.length > 255) {
        this.formError = {
          name: {
            status: true,
            msg: this.$t('Folder_toolong'),
          },
        };
      } else if (key == 'content' && this.formInfo.content.length > 3000) {
        this.formError = {
          content: {
            status: true,
            msg: this.$t('Folder_toolong'),
          },
        };
      } else {
        delete this.formError[key];
      }
    },
    saveDefine() {
      if (this.formInfo.name == '') {
        this.formError = {
          name: {
            status: true,
            msg: this.$t('Custom_template_edit_name_error'),
          },
        };
        return false;
      } else if (this.formInfo.name.length > 255) {
        this.formError = {
          name: {
            status: true,
            msg: this.$t('Folder_toolong'),
          },
        };
        return false;
      }
      if (this.formInfo.content == '') {
        this.formError = {
          content: {
            status: true,
            msg: this.$t('Custom_template_edit_prompt_error'),
          },
        };
        return false;
      } else if (this.formInfo.content.length > 3000) {
        this.formError = {
          content: {
            status: true,
            msg: this.$t('Folder_toolong'),
          },
        };
        return false;
      }
      this.formError = {};
      this.isLoading = true;
      let PM = null;
      if (this.formInfo.id) {
        PM = this.reqPatchInfo('/ai/customtemplates/' + this.formInfo.id, this.formInfo);
      } else {
        PM = this.reqPostInfo('/ai/customtemplates', this.formInfo);
      }
      PM.then(
        (data) => {
          // this.getDefineList();
          this.isLoading = false;
          this.defineStatus = false;
        },
        (data) => {
          if (data.status == -1) {
            //没有权限
            this.isLoading = false;
            this.setMessage('no permission');
          } else if (data.status == -2) {
            //超出个数限制
            this.isLoading = false;
            this.setMessage(this.$t('Custom_template_toast_maxlimit'));
          }
        },
      );
    },
    deleteDefine() {
      this.setConfirm({
        title: this.$t('Custom_template_edit_btn_delete'),
        msg: this.getTranslateStr('Custom_template_edit_delete_caution'),
        ok: () => {
          this.isLoading = true;
          this.reqDeleteInfo('/ai/customtemplates/' + this.formInfo.id).then(
            () => {
              if (this.summaryType == this.formInfo.id) {
                this.summaryType = '';
              }
              // this.getDefineList();
              this.isLoading = false;
              this.defineStatus = false;
            },
            (data) => {
              if (data.status == -1) {
                //没有权限
                this.isLoading = false;
                this.setMessage('no permission');
              } else if (data.status == -2) {
                //template not found
                this.isLoading = false;
                this.setMessage('template not found');
              }
            },
          );
        },
        cancel: () => {},
      });
    },
    formatIcon(icon) {
      return findIconCodeToName(icon);
    },
    formatColor(color) {
      return convertToHexColor(color);
    },
    showAutoTips(lng) {
      return lng.id == 'auto' && this.pageFlag === 2 && !this.enableSummaryAuto;
    },
    revertTranSummaryDialog() {
      // console.log('revertTranSummaryDialog-0604');
      this.defineStatus = false;
      this.hideAllViewDialogModal(false);
    },
    closeTranSummaryDialog() {
      this.dialogStatus = false;
      this.defineStatus = false;
      this.summaryType = '';
      this.hideAllViewDialogModal(false);
    },
    handleExpand(item, index) {
      // console.log('handleExpand-0602:', this.categoryList, index);
      this.defineStatus = true;
      this.hideAllViewDialogModal(true);
      this.$emit('preview', { data: this.categoryList, index });
    },
    // 隐藏allViewDialog modal层
    hideAllViewDialogModal(flag) {
      let str = flag ? 'none' : '';
      document.querySelectorAll('.v-modal').forEach((el) => (el.style.display = str));
    },
    // 点击预览右上角Use
    useTemplate(obj) {
      // console.log('useTemplate-gsy-data-obj-data:', obj);
      // console.log('useTemplate-gsy-this.selectName:', this.selectName);
      let { data, index } = obj;
      this.doSelectTemplate('overallDialog', data, index);
      /**
       * 找到匹配的项目并移到最前面，匹配当前类别/最近使用/customize，保证selectName与当前类别一致
       * 1.如果匹配到customize，选中项移动到最前面
       * 2.如果匹配summaryList，选中项移动到类别最前面
       * 3.如果匹配到最近使用，选中项移动到最前面
       */
      if (this.customList.length > 0) {
        if (this.customList.some((item) => item.id === this.summaryType)) {
          // console.log('useTemplate-gsy-data-obj-customList:', this.customList);
          // 找到匹配的项并移到第一项
          const matchedIndex = this.customList.findIndex((item) => item.id === this.summaryType);
          if (matchedIndex !== -1 && matchedIndex > 0) {
            const selected = this.customList.splice(matchedIndex, 1)[0];
            this.customList.unshift(selected);
          }
          // console.log('this.customList-gsy-after:', this.customList);
          if (this.selectName === this.customCategory) {
            this.categoryList = this.customList;
          }
          // 如果customList匹配到，直接return，后续不再执行
          this.initalScroll();
          // return;
        }
      }

      if (this.summaryList.length > 0) {
        if (this.summaryList.some((item) => item.category === data.category)) {
          // console.log('useTemplate-gsy-data-obj-summaryList:', this.summaryList);
          // 找到匹配的项并移到第一项
          const matchedCategoryIndex = this.summaryList.findIndex((item) =>
            item.data.some((template) => template.category === data.category),
          );

          if (matchedCategoryIndex !== -1) {
            const matchedCategory = this.summaryList[matchedCategoryIndex];
            // 再把data里选中的项移到data第一项
            const selectedIndex = matchedCategory.data.findIndex((item) => {
              if (item.custom_type === 'community') {
                return item[this.translateKey].id === this.summaryType;
              } else {
                return item.type === this.summaryType;
              }
            });

            if (selectedIndex > 0) {
              const selected = matchedCategory.data.splice(selectedIndex, 1)[0];
              matchedCategory.data.unshift(selected);
            }
          }
        }
      }

      if (this.recentUseList.length > 0) {
        if (
          this.recentUseList.some(
            (item) =>
              item.type === this.summaryType ||
              item.id === this.summaryType ||
              item.summaryType === this.summaryType,
          )
        ) {
          const rIndex = this.recentUseList.findIndex(
            (item) =>
              item.type === this.summaryType ||
              item.id === this.summaryType ||
              item.summaryType === this.summaryType,
          );
          // console.log('rIndex-0609:', rIndex);
          const selected = this.recentUseList.splice(rIndex, 1)[0];
          this.recentUseList.unshift(selected);
        }
      }
      this.initalScroll();
    },
    saveCustomTemplateSuccess(obj) {
      let res = {
        ...obj,
        custom_type: 'custom',
      };
      // console.log('saveCustomTemplateSuccess-gsy-res:', res);
      this.customList.unshift({ ...res, custom_status: undefined });
      this.categoryList = this.customList;
      this.initalScroll();
      this.doSelectTemplate('overallDialog', res, 0);
      this.revertTranSummaryDialog();
    },
    // 隐藏页面.middleHide 小图标
    // hideMiddleIcon(flag) {
    //   let str = flag ? 'none' : '';
    //   document.querySelectorAll('.middleHide').forEach((el) => {
    //     el.style.display = str;
    //   });
    // },
  },
};
</script>
<style scope lang="less">
.viewAllDialog {
  background-color: #f0f2f6;
  padding: 0;
  overflow: hidden;
}
.red-sign {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #f14349;
  margin-left: 5px;
}
.loading-center {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px; /* 可根据实际内容区高度调整 */
  //margin-left: -100px;
}
</style>
