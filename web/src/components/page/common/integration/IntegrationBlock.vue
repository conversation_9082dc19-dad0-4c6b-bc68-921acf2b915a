<template>
  <div :class="`integration-main integration-${lowerType}`">
    <div class="layer" v-if="showDisconnect" @click="showDisconnect = false"></div>
    <div class="top">
      <template v-if="isTypeMore">
        <div
          class="img-list"
          ref="imgList"
          @mouseenter="scrollSpeed = 0.1"
          @mouseleave="scrollSpeed = 0.3"
        >
          <img
            class="logo"
            v-for="(item, index) in duplicatedImgList"
            :key="index"
            :src="getImagePath(item)"
          />
        </div>
      </template>
      <template v-else>
        <img class="logo" v-if="iconName" :src="getImagePath(iconName)" />
        <div class="title">{{ name }}</div>
      </template>
      <div class="circle-red" v-if="!isTypeMore && showCircle"></div>
      <div class="connected" v-if="!isTypeMore && isConnected">{{ $t('connected_text') }}</div>
    </div>
    <div class="content">
      <template v-if="type === 'Zapier'">
        <div class="text">{{ $t('zapier_descript') }}</div>
        <div class="link" @click="goLink">{{ $t('zapier_help') }}</div>
      </template>
      <template v-else-if="isTypeMore">
        <div class="subtitle">{{ getTranslateStr('connect_tag_one', '%s', 'More') }}</div>
        <div class="text">{{ $t('connect_tag_two') }}</div>
      </template>
    </div>
    <div class="bottom">
      <div class="bottom-wrapper">
        <div class="logoutBtn btn" @click="handleBtnClick">
          <div class="text">{{ btnText }}</div>
          <img class="skip" :src="getImagePath('icon_skip')" v-if="!isTypeMore" />
        </div>
        <div class="more" v-if="!isTypeMore && isConnected">
          <img :src="getImagePath('icon_plus')" @click="showDisconnect = !showDisconnect" />
          <div class="disconnect" v-if="showDisconnect">
            <div class="menu" @click="handleDisConnect">
              {{ $t('disconnect_text') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import { getFrillUrl, FRILL_DATA } from '@/util/frill';
import { getAuthURLs } from '@/apis/auth';
import { storage } from '@/util/storage';

export default {
  components: {},
  props: {
    name: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      required: true,
    },
    iconName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      btnText: '',
      isConnected: false, // 连接状态
      showDisconnect: false, // 展示断连菜单
      scrollSpeed: 0.3,
      animationFrame: null, // 用于存储 requestAnimationFrame 的 ID
      showCircle: false,
      imgList: [
        'icon_integration_01',
        'icon_integration_02',
        'icon_integration_03',
        'icon_integration_04',
        'icon_integration_05',
        'icon_integration_06',
        'icon_integration_07',
        'icon_integration_08',
        'icon_integration_09',
        'icon_integration_10',
        'icon_integration_11',
        'icon_integration_12',
        'icon_integration_13',
        'icon_integration_14',
        'icon_integration_15',
        'icon_integration_16',
      ],
      authURLs: {},
    };
  },
  computed: {
    duplicatedImgList() {
      return this.imgList.concat(this.imgList);
    },
    lowerType() {
      return this.type.toLowerCase();
    },
    isTypeMore() {
      return this.type === 'More';
    },
    ...mapState({
      frillSsoToken: (state) => state.sso.frillSsoToken, // Frill ssoToken
      userInfo: (state) => state.auth.userInfo, // Frill ssoToken
      redDotShowData: (state) => state.auth.redDotShowData,
    }),
    ...mapGetters('auth', ['zapierRedDotShow', 'email']),
  },
  watch: {
    isConnected: {
      handler(newData, oldData) {
        this.initBtnText();
      },
      immediate: true,
    },
  },
  created() {
    this.init();
  },
  mounted() {
    this.startScroll(); // 组件挂载后启动滚动
    this.setViewTrackingEvent();
  },
  beforeDestroy() {
    this.stopScroll(); // 组件销毁前停止滚动
    this.changeRedDot();
  },
  methods: {
    ...mapMutations('auth', ['AUTH_SET_REDDOTSHOW']),
    init() {
      this.getApi();
      this.initShowRedCircle();
      this.getConnectStatus();
    },
    changeStorageRedDot() {
      // 遍历 this.redDotShowData 对象，将除了 frill 之外的其他属性值都重置为 false
      const updatedRedDotShowData = { ...this.redDotShowData };
      for (const key in updatedRedDotShowData) {
        if (key !== 'frill') {
          updatedRedDotShowData[key] = false;
        }
      }
      storage.set('redDotShow', updatedRedDotShowData, this.email, 'NoExpiration');
    },
    changeRedDot() {
      this.AUTH_SET_REDDOTSHOW({
        name: this.email,
        data: { [this.lowerType]: false },
      });
    },
    getApi() {
      this.authURLs = getAuthURLs(this.type.toLowerCase());
    },
    initBtnText() {
      switch (this.type) {
        case 'More':
          this.btnText = this.$t('suggest_integration_text');
          break;
        default:
          this.btnText = this.isConnected
            ? this.getTranslateStr('go_to_app_text', '%s', this.type)
            : this.$t('connect_text');
          break;
      }
    },
    initShowRedCircle() {
      switch (this.type) {
        case 'Zapier':
          this.showCircle = this.zapierRedDotShow;
          break;
        default:
          this.showCircle = false;
          break;
      }
      this.changeStorageRedDot();
    },
    getImagePath(icon) {
      try {
        return require(`../../../../../public/static/${icon}.png`);
      } catch (e) {
        console.error(`Image not found: ../../../../../public/static/${icon}.png`, e);
        return '';
      }
    },
    // 获取连接状态
    async getConnectStatus() {
      if (this.isTypeMore) return;
      try {
        const data = await this.reqGetInfo(this.authURLs.connect, {});
        console.log('success', data);
        this.isConnected = data?.msg === 'connected';
      } catch (error) {
        console.log('fail', error);
        this.isConnected = false;
      }
    },
    // 解除授权
    async handleDisConnect() {
      this.showDisconnect = false;
      this.setConfirm({
        title: this.$t('disconnect_title'),
        msg: this.$t('disconnect_content'),
        okname: this.$t('disconnect_text'),
        cancelname: this.$t('Manage_logins_device_logout_button_left'),
        hasClose: false,
        ok: async () => {
          this.$emit('loadingStatus', true);
          try {
            const data = await this.reqDeleteInfo(this.authURLs.connect, {});
            this.isConnected = data?.msg === 'disconnected';
            this.$emit('loadingStatus', false);
            // this.showSuccessMsg(this.$t('disconnect_message'));
            this.setMessage({
              message: this.$t('disconnect_message'),
              autoClose: true,
              type: 'success',
              duration: 3 * 1000,
            });
          } catch (error) {
            console.log('disconnect failed', error);
          }
        },
      });
    },
    goLink() {
      window.open(
        'https://support.plaud.ai/hc/en-us/articles/12200669941647-PLAUD-Zapier-Integration',
      );
    },
    handleBtnClick() {
      switch (this.type) {
        case 'Zapier':
          // 判断是 connect 状态
          if (this.btnText === this.$t('connect_text')) {
            // 点击Zapier组件中Connect按钮的次数
            this.setPlaudLogEvent('zapier', { action: 'clickConnect' });
          }
          window.open('https://zapier.com/apps/plaud/integrations');
          break;
        case 'More':
          let integrationURL = FRILL_DATA.integration;
          let url = getFrillUrl(integrationURL, this.frillSsoToken);
          window.open(url);
          break;
      }
    },
    startScroll() {
      if (this.type !== 'More') return;
      const imgListElement = this.$refs.imgList;
      let scrollLeft = 0;
      const scroll = () => {
        scrollLeft += this.scrollSpeed; // 每次滚动 scrollSpeed 像素
        if (scrollLeft >= imgListElement.scrollWidth / 2) {
          scrollLeft = 0; // 滚动到一半时重置
        }
        imgListElement.style.transform = `translateX(-${scrollLeft}px)`;
        this.animationFrame = requestAnimationFrame(scroll); // 递归调用
      };

      this.animationFrame = requestAnimationFrame(scroll); // 启动滚动
    },
    stopScroll() {
      if (this.type !== 'More') return;
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame); // 停止滚动
      }
    },
    // integration组件的View次数
    setViewTrackingEvent() {
      switch (this.type) {
        case 'Zapier':
          this.setPlaudLogEvent('zapier', { action: 'view' });
          break;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.integration-main {
  // overflow: hidden;
  padding-right: 16px;
}
.integration-more {
  padding-right: 0;
  overflow: hidden;
  .top {
    mask-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0) 0%,
      rgb(0, 0, 0) 12.5%,
      rgb(0, 0, 0) 87.5%,
      rgba(0, 0, 0, 0) 100%
    );
  }
}
.layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  // background: red;
  // opacity: 0.2;
}
.top {
  height: 32px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  position: relative;
  .img-list {
    display: flex;
    flex-direction: row;
    white-space: nowrap; /* 防止换行 */
    will-change: transform; /* 优化性能 */
    cursor: pointer;
    img {
      margin-right: 6px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .logo {
    width: 32px;
    height: 32px;
  }
  .title {
    margin-left: 8px;
    font-size: 14px;
    font-weight: 590;
    line-height: 20px;
    color: #1f1f1f;
  }
  .circle-red {
    position: absolute;
    right: 0;
    top: 0;
  }
  .connected {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #34c759;
    font-weight: 590;
    font-size: 12px;
  }
}
.content {
  margin-bottom: 20px;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  color: #858c9b;
  word-wrap: break-word;
  .link {
    color: #007aff;
    cursor: pointer;
  }
  .subtitle {
    color: #1f1f1f;
    font-size: 14px;
    line-height: 20px;
    font-weight: 590;
  }
}
.bottom {
  display: flex;
  align-items: center;
  margin-right: 12px;
  height: 31px;
  .bottom-wrapper {
    position: absolute;
    left: 15px;
    bottom: 16px;
    display: flex;
    align-items: center;
  }
  .btn {
    width: fit-content;
    display: flex;
    align-items: center;
    line-height: 20px;
    font-size: 14px;
    .skip {
      width: 14px;
      height: 14px;
      margin-left: 4px;
    }
  }
  .more {
    width: 32px;
    height: 32px;
    position: relative;
    margin-left: 12px;
    border-radius: 6px;
    font-size: 14px;
    background: transparent;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      background: white;
    }
    img {
      width: 20px;
      height: 20px;
      // position: absolute;
      // top: 50%;
      // left: 50%;
      // transform: translate(-50%, -50%);
    }
    .disconnect {
      z-index: 20;
      position: absolute;
      right: 0;
      top: 38px;
      padding: 8px 14px;
      display: flex;
      flex-direction: column;
      border-radius: 14px;
      background: white;
      width: 192px;
      justify-content: center;
      color: #1f1f1f;
      box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
      .menu {
        height: 36px;
        line-height: 36px;
      }
    }
  }
}
</style>
