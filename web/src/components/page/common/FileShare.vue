<template>
  <div class="rightIcons">
    <div class="free-btn" data-pld="web_member_gift_free" @click="$parent.handleMemberCheck(-1)">
      <div class="free-gift-img"></div>
    </div>
    <el-popover
      popper-class="elPopoverClass"
      placement="bottom-end"
      width="300"
      @show="showShareWin(1)"
      @hide="shareFlag = -1"
      :visible-arrow="false"
      trigger="click"
    >
      <div class="shareExport">
        <ul class="shareUlList hand export nobg">
          <template v-for="(info, index) in exportList">
            <li
              :class="{ active: exportInfo.name === info.name }"
              @click="checkExport(info)"
              v-show="!(!$parent.selectFileInfo.is_trans && index > 1)"
            >
              <div class="iconfont" :class="[info.icon]"></div>
              <div class="name">{{ info.name }}</div>
            </li>
            <div class="dline" v-if="index === 0"></div>
          </template>
        </ul>
      </div>
      <template v-slot:reference>
        <div class="label-container">
          <div class="iconfont myIcon icon-icon_share" :class="{ active: shareFlag === 1 }"></div>
          <span class="label">{{ $t('share') }}</span>
        </div>
      </template>
    </el-popover>
    <el-popover
      popper-class="elPopoverClass"
      placement="bottom-end"
      width="280"
      @show="showMoreWin"
      @hide="showMore = false"
      :visible-arrow="false"
      trigger="click"
    >
      <div class="shareExport">
        <ul class="shareUlList hand export nobg">
          <template v-for="(info, index) in moreList">
            <div class="dline" v-if="info.id === 'trash'"></div>
            <li
              :class="{ active: moreInfo.id === info.id, trash: info.id === 'trash' }"
              @click="checkMore(info)"
              v-show="
                !(!$parent.selectFileInfo.is_trans && index > 0 && index != moreList.length - 1) &&
                !info.disabled
              "
            >
              <div class="iconfont" :class="[info.icon]"></div>
              <div class="name">{{ info.name }}</div>
            </li>
          </template>
        </ul>
      </div>
      <template v-slot:reference>
        <div class="label-container">
          <div class="iconfont myIcon icon-icon_more_circle" :class="{ active: showMore }"></div>
          <span class="label">{{ $t('more') }}</span>
        </div>
      </template>
    </el-popover>
    <MyDialog
      :title="exportInfo.name"
      width="375px"
      :visible="dialogStatus"
      @close="dialogStatus = false"
      :toBody="true"
    >
      <template v-slot:content>
        <div class="shareExport" v-if="exportInfo.id == 'shareurl'">
          <ul class="shareUlList">
            <li
              v-for="(info, index) in shareList"
              :class="{ active: exportInfo.name === info.name }"
              v-show="!(!$parent.selectFileInfo.is_trans && index > 0)"
              @click="checkShare(info.key, index)"
            >
              <div class="iconfont" :class="[info.icon]"></div>
              <div class="name">{{ info.name }}</div>
              <div class="lsCheck" :class="{ active: shareInfo[info.key] }"></div>
            </li>
          </ul>
          <Loading :status="shareLoading" />
          <template>
            <div class="formError">{{ errorMsg }}</div>
            <div class="desc">{{ $t('Filedetail_share_link_tips') }}</div>
            <div class="commonBtn" style="margin-top: 16px" @click="getShareUrl()">
              {{ $t('Filedetail_share_buttontwo') }}
            </div>
          </template>
        </div>
        <div v-else>
          <div class="exportPopTitle">
            {{
              exportInfo.id === 'audio'
                ? $t('Filedetail_share_audio_subtitleone')
                : $t('Filedetail_share_exportas')
            }}
          </div>
          <ul class="shareUlList hand export">
            <li
              v-for="info in exportInfo.children"
              :class="{ active: checkExportInfo.name === info.name }"
              @click="doCKExport(info)"
            >
              <div class="iconfont" :class="[info.icon]"></div>
              <div class="name">{{ info.name }}</div>
              <div class="iconfont icon-selected" v-if="checkExportInfo.name === info.name"></div>
            </li>
          </ul>
          <Loading :status="isLoading" />
          <div class="formError">{{ errorMsg }}</div>
          <div class="commonBtn" style="margin-top: 10px" @click="doExport()">
            {{ $t('Filedetail_share_export') }}
          </div>
        </div>
      </template>
    </MyDialog>
    <!-- <EditName ref="editName" /> -->
  </div>
</template>

<script>
import MyDialog from '../../common/MyDialog.vue';
// import EditName from './EditName';
import ExportTool from '../../../util/ExportTool';
import formatDate from '../../../util/format-date';
import { downloadFile } from '../../../util/file';

// import MavonEditor from 'mavon-editor';
// import 'mavon-editor/dist/css/index.css';
// Vue.use(MavonEditor);

export default {
  components: { MyDialog },
  computed: {
    hacCheck() {
      let has = false;
      for (let k in this.shareInfo) {
        if (this.shareInfo[k] === 1) {
          has = true;
          break;
        }
      }
      return has;
    },
    moreList() {
      return [
        // { id: 'edit', name: this.$t('Filelist_selected_rename'), icon: 'icon-a-Editfilename' },
        { id: 'move', name: this.$t('Web_filelist_selected_move'), icon: 'icon-a-moveto' },
        {
          id: 'find',
          name: this.$t('find_placeholder'),
          icon: 'icon-a-search',
          disabled: this.$parent.generateStatus > 0 || this.$parent.isLoading,
        },
        {
          id: 'retran',
          name: this.$t('Filedetail_retranscribe'),
          icon: 'icon-icon_transcript',
          disabled: this.$parent.generateStatus > 0 || this.$parent.isLoading,
        },
        {
          id: 'resum',
          name: this.$t('Filedetail_resummarize'),
          icon: 'icon-re-summarize',
          disabled: this.$parent.generateStatus > 0 || this.$parent.isLoading,
        },
        { id: 'trash', name: this.$t('Filelist_selected_trash'), icon: 'icon-trash' },
      ];
    },
  },
  data() {
    return {
      errorMsg: '',
      showMore: false,
      shareFlag: -1,
      shareList: [
        { name: this.$t('Filedetail_share_link_audio'), key: 'is_audio', icon: 'icon-Audio' },
        {
          name: this.$t('Filedetail_share_link_summary'),
          key: 'is_ai_content',
          icon: 'icon-summary',
        },
        {
          name: this.$t('Filedetail_share_link_mindmap'),
          key: 'is_mindmap',
          icon: 'icon-mind-map',
        },
        {
          name: this.$t('Filedetail_share_link_transcription'),
          key: 'is_trans',
          icon: 'icon-transcription',
        },
      ],
      shareInfo: {
        is_audio: 0,
        is_trans: 0,
        is_ai_content: 0,
        is_mindmap: 0,
      },
      exportList: [
        {
          id: 'shareurl',
          name: this.$t('Filedetail_share_link'),
          icon: 'icon-links',
        },
        {
          id: 'audio',
          name: this.$t('Filedetail_share_audioexport'),
          icon: 'icon-Audio',
          children: [
            {
              id: 'mp3',
              name: this.$t('Filedetail_share_audio_subtitleone_formatone'),
              icon: 'icon-MP3',
            },
            // {id:'wav',name:this.$t('Filedetail_share_audio_subtitleone_formattwo'),icon:'icon-WAN'}
          ],
        },
        {
          id: 'transcription',
          name: this.$t('Filedetail_share_transcriptionexport'),
          icon: 'icon-transcription',
          children: [
            {
              id: 'txt',
              name: this.$t('Filedetail_share_transcription_subtitleone_formatone'),
              icon: 'icon-TXT',
              logkey: 'web_export_trans_txt',
            },
            {
              id: 'srt',
              name: this.$t('Filedetail_share_transcription_subtitleone_formatthree'),
              icon: 'icon-SRT',
            },
            {
              id: 'docx',
              name: this.$t('Filedetail_share_transcription_subtitleone_formattwo'),
              icon: 'export_word',
              logkey: 'web_export_trans_docx',
            },
            {
              id: 'pdf',
              name: this.$t('Filedetail_share_transcription_subtitleone_formatfour'),
              icon: 'icon-PDF',
            },
          ],
        },
        {
          id: 'summary',
          name: this.$t('Filedetail_share_summaryexport'),
          icon: 'icon-summary',
          children: [
            {
              id: 'txt',
              name: this.$t('Filedetail_share_transcription_subtitleone_formatone'),
              icon: 'icon-TXT',
              logkey: 'web_export_summary_txt',
            },
            {
              id: 'markdown',
              name: this.$t('Filedetail_share_summary_formattwo'),
              icon: 'icon-Markdown',
              logkey: 'web_export_summary_md',
            },
            {
              id: 'docx',
              name: this.$t('Filedetail_share_transcription_subtitleone_formattwo'),
              icon: 'export_word',
              logkey: 'web_export_summary_docx',
            },
            {
              id: 'pdf',
              name: this.$t('Filedetail_share_transcription_subtitleone_formatfour'),
              icon: 'icon-PDF',
            },
          ],
        },
        {
          id: 'mindmap',
          name: this.$t('Filedetail_share_mindmapexport'),
          icon: 'icon-mind-map',
          children: [
            // {id:'xmind',name:this.$t('Filedetail_share_mindmap_formatone'),icon:'icon-Xmind'},
            {
              id: 'markdown',
              name: this.$t('Filedetail_share_summary_formattwo'),
              icon: 'icon-Markdown',
              logkey: 'web_export_mindmap_md',
            },
            { id: 'jpeg', name: 'PNG', icon: 'icon-Jpg', logkey: 'web_export_mindmap_png' },
            // {id:'pdf',name:this.$t('Filedetail_share_transcription_subtitleone_formatfour'),icon:'icon-PDF'}
          ],
        },
      ],
      exportInfo: {},
      checkExportInfo: {},
      dialogStatus: false,
      shareLoading: false,
      moreInfo: {},
      msgDom: null,
      summaryContent: {},
    };
  },
  methods: {
    showShareWin(flag) {
      if (flag === 0) {
        this.setGALogEvent('web_share');
      } else if (flag === 1) {
        this.setGALogEvent('web_export');
      }
      this.shareFlag = flag;
      // 从 localStorage 获取数据
      const shareCheck = localStorage.getItem('shareCheck');
      if (shareCheck) {
        // 解析 JSON 字符串并赋值给 data 中的 myData
        this.shareInfo = JSON.parse(shareCheck);
      } else {
        this.shareInfo = {
          is_audio: 1,
          is_trans: 1,
          is_ai_content: 1,
          is_mindmap: 1,
        };
      }

      this.exportInfo = {};
      this.checkExportInfo = {};
      this.summaryContent = this.getSummaryContent();
      this.errorMsg = '';
    },
    checkShare(key, index) {
      let kv = this.shareInfo[key];
      this.$set(this.shareInfo, key, kv === 0 ? 1 : 0);
      this.errorMsg = '';
    },
    // 保存数据到 localStorage 的方法
    saveToLocalStorage() {
      // 将对象转为 JSON 字符串并存储
      localStorage.setItem('shareCheck', JSON.stringify(this.shareInfo));
    },
    getShareUrl() {
      // console.log(
      //   this.$parent.selectFileInfo,
      //   'this.$parent.selectFileInfo',
      //   this.checkExportInfo.id,
      // );
      if (!this.hacCheck) {
        this.errorMsg = this.$t('Filedetail_share_noshare');
        return false;
      }
      this.shareLoading = true;
      this.setGALogEvent('web_share_url', this.shareInfo, this.$parent.selectFileInfo.id,this.summaryContent);
      let webLang = window.localStorage.getItem('pweblang') || this.$i18n.locale || 'en_US';
      this.saveToLocalStorage();
      this.reqPostInfo('/file/share-url/' + this.$parent.selectFileInfo.id, this.shareInfo).then(
        (data) => {
          this.shareLoading = false;
          if (data.status === 0) {
            let shareUrl = data.url;
            if (location.hostname.indexOf('plaud.ai') === -1) {
              let shareKey = shareUrl.substring(shareUrl.lastIndexOf('/') + 1);
              // shareUrl = 'http://localhost:3849/share#/' + shareKey + '?lang=' + webLang;
              shareUrl =
                'https://plaud-web-dist-test.pages.dev/share#/' + shareKey + '?lang=' + webLang;
            }
            this.$copyText(shareUrl).then(
              (e) => {
                this.setMessage(this.$t('Web_filedetail_share_link_copied_toast'));
                this.dialogStatus = false;
              },
              (e) => {
                // this.setMessage('copy fail');
              },
            );
          }
        },
        (data) => {
          if (data.status === -1) {
            // this.setMessage('file not found');
          }
        },
      );
    },
    checkExport(info) {
      this.exportInfo = info;
      this.dialogStatus = true;
      this.domClick();
      // 音频只有一个选项，直接选中即可
      if (info.id === 'audio') {
        this.doCKExport(info.children[0]);
      }
    },
    doCKExport(info) {
      if (info.id == 'mp3') {
        this.setGALogEvent('web_export_recording_mp3', {}, this.$parent.selectFileInfo.id,this.summaryContent);
      }
      this.checkExportInfo = info;
      this.errorMsg = '';
    },
    sendAudioLog() {
      let { duration } = this.$parent.selectFileInfo,
        flag = 0,
        mins = duration / 1000 / 60;
      if (mins <= 30) {
        flag = 0;
      } else if (mins > 30 && mins <= 60) {
        flag = 1;
      } else if (mins > 60 && mins <= 90) {
        flag = 2;
      } else if (mins > 90 && mins <= 120) {
        flag = 3;
      } else {
        flag = 4;
      }
      this.setGALogEvent(
        [
          'web_export_recording_30m',
          'web_export_recording_1h',
          'web_export_recording_1h30m',
          'web_export_recording_2h',
          'web_export_recording_m2h',
        ][flag],
        {},
        this.$parent.selectFileInfo.id,
        this.summaryContent
      );
    },
    doExport() {
      console.log(
        this.$parent.selectFileInfo,
        'this.$parent.selectFileInfo',
        this.checkExportInfo.id,
      );
      if (this.checkExportInfo.id) {
        let fileInfo = this.$parent.selectFileInfo,
          mId = this.checkExportInfo.id,
          pId = this.exportInfo.id;
        if (this.checkExportInfo.logkey) {
          this.setGALogEvent(this.checkExportInfo.logkey, {}, this.$parent.selectFileInfo.id,this.summaryContent);
        }
        let {ai_content,category} = this.summaryContent;
        switch (mId) {
          case 'wav':
          case 'mp3':
            this.sendAudioLog();
            this.msgDom = this.showBackMsg(this.$t('Filedetail_share_audio_button_toast'));
            this.isLoading = true;
            let startTime = new Date().getTime();
            this.reqGetInfo('/file/temp-url/' + fileInfo.id).then(async (audata) => {
              if (audata.status === 0) {
                await this.downloadFile(audata.temp_url, fileInfo.filename + '.mp3');

                this.isLoading = false;
                let userTime = (new Date().getTime() - startTime) / 1000,
                  flag = 0;
                if (userTime < 15) {
                  flag = 0;
                } else if (userTime >= 15 && userTime < 30) {
                  flag = 1;
                } else if (userTime >= 30 && userTime < 45) {
                  flag = 2;
                } else if (userTime >= 45 && userTime < 60) {
                  flag = 3;
                } else if (userTime >= 60 && userTime < 90) {
                  flag = 4;
                } else if (userTime >= 90 && userTime < 120) {
                  flag = 5;
                } else if (userTime >= 120 && userTime < 180) {
                  flag = 6;
                } else {
                  flag = 7;
                }
                this.setGALogEvent(
                  [
                    'web_export_recording_15s',
                    'web_export_recording_30s',
                    'web_export_recording_45s',
                    'web_export_recording_60s',
                    'web_export_recording_90s',
                    'web_export_recording_120s',
                    'web_export_recording_180s',
                    'web_export_recording_m180s',
                  ][flag],
                  {},
                  this.$parent.selectFileInfo.id,
                  this.summaryContent
                );
                this.clearMsg();
              }
            });
            break;
          case 'jpeg':
            this.$emit('doMenu', this.checkExportInfo);
            break;
          case 'txt':
          case 'docx':
          case 'pdf':
          case 'markdown':
          case 'srt':
            let fileData = '',
              fileName = fileInfo.filename + (pId != 'transcription' && category ? `-${category}` : '');
            if (pId == 'transcription') {
              if (mId == 'docx') {
                fileData = this.getTransData().join('<br/>');
              } else {
                fileData = this.getTransData().join('\n');
              }
            } else if (pId == 'summary' || pId == 'mindmap') {
              // if (mId == 'docx') {
              //   fileData = document
              //     .getElementById('mdhtml')
              //     .querySelector('.v-note-show').innerHTML;
              // } else {
              // }
              fileData = ai_content;
            }
            if (fileData != '') {
              if (mId == 'txt') {
                ExportTool.exportTxt(fileData, fileName);
              } else if (mId == 'docx' || mId == 'pdf' || mId == 'srt') {
                const params = {
                  file_id: fileInfo.id,
                  prompt_type: 'trans',
                  to_format: mId.toUpperCase(),
                  title: fileInfo.filename,
                  create_time: formatDate('yyyy-MM-dd hh:mm:ss', fileInfo.start_time),
                  with_speaker: 1,
                  with_timestamp: 1,
                };
                if (pId == 'transcription') {
                  params.trans_content = fileInfo.trans_result;
                } else if (pId == 'summary') {
                  params.prompt_type = 'summary';
                  params.summary_content = ai_content;
                }
                this.reqPostInfo('/file/document/export', params).then((data) => {
                  downloadFile(data, fileName + '.' + mId);
                });
              } else if (mId == 'markdown') {
                ExportTool.exportMd(fileData, fileName);
              }
            }
            break;
        }
        this.dialogStatus = false;
      } else {
        this.errorMsg = this.$t('Filedetail_export_noformat');
      }
    },
    getSummaryContent(){
      let {ai_content,summaryTab} = this.$parent.selectFileInfo;
      if(ai_content && ai_content.startsWith('[{')){
        let contentList = JSON.parse(ai_content);
        if(contentList.length > 0){
          let activeSummary = contentList.find(item=>item.original_category === summaryTab) ?? contentList[0];
          return activeSummary;
        }
        return {ai_content:''};
      }
      return {ai_content};
    },
    getTransData() {
      let myDatas = [];
      this.$parent.selectFileInfo.trans_result.forEach((item) => {
        myDatas.push(item.content);
      });
      return myDatas;
    },
    showMoreWin() {
      this.moreInfo = {};
      this.showMore = true;
    },
    checkMore(info) {
      this.moreInfo = info;
      switch (info.id) {
        // case 'edit':
        //   this.setGALogEvent(
        //     this.!$parent.selectFileInfo.is_trans ? 'web_edit_filename_notrans' : 'web_edit_filename_trans',
        //     {},
        //     this.$parent.selectFileInfo.id,
        //   );
        //   this.$refs.editName.handlerVisible(true, this.$parent.selectFileInfo.filename);
        //   break;
        case 'find':
          // TODO: replace 全局变量
          window.__onTrigger__ && window.__onTrigger__('SEARCH');
          break;
        case 'retran':
        case 'resum':
        case 'move':
        case 'trash':
        case 'audiotrim':
          this.$emit('doMenu', info);
          break;
      }
      this.domClick();
    },
    clearMsg() {
      if (this.msgDom) {
        this.msgDom.onClose();
        this.msgDom = null;
      }
    },
  },
  beforeUnmount() {
    this.clearMsg();
  },
};
</script>

<style lang="less" scoped>
.label-container {
  position: relative;
}
.label {
  position: absolute;
  top: 36px;
  right: -5px;
  font-size: 12px;
  background-color: #333;
  font-weight: 400;
  /* 默认隐藏 label */
  display: none;
  height: 24px;
  line-height: 16px;
  color: white;
  border-radius: 4px;
  padding: 4px 6px;
  width: max-content;
}

.label-container:hover .label {
  /* 鼠标悬停时显示 label */
  display: block;
}
</style>
<style>
.free-btn {
  border-radius: 8px;
  border: 1.5px solid #bc82f3;
  background: linear-gradient(0deg, rgba(238, 228, 255, 0.4) 0%, rgba(238, 228, 255, 0.4) 100%);
  height: 34px;
  width: 34px;
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
</style>
