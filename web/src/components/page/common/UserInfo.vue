<template>
  <div class="userCenterBox">
    <div class="memberInfo">
      <div class="flex-middle">
        <div class="uNameIcon">
          <img :src="userInfo.avatar" v-if="userInfo.avatar" />
          <span v-else>{{ userInfo.nickname ? userInfo.nickname.substring(0, 1) : '' }}</span>
        </div>
        <div class="nameInfo">
          <div class="name">{{ userInfo.nickname }}</div>
          <div v-if="isEmailShow" class="email">{{ userInfo.email }}</div>
        </div>
      </div>
      <div class="iconfont icon-next flex-center backSetBtn" @click="$parent.goTo(1)"></div>
    </div>
    <div class="totalInfo">
      <div class="totalItem" v-for="info in totalList">
        <div class="num">
          {{ info.key.includes('_duration') ? getHourStr(statInfo[info.key]) : statInfo[info.key] }}
        </div>
        <div class="label">{{ info.name }}</div>
      </div>
    </div>
    <div class="flex-between hotBox">
      <div class="hotTitle">
        {{ $t('Me_calendar_tips') }}
      </div>
      <div class="hotLeg">
        {{ $t('Me_calendar_less') }}
        <div class="hotItems">
          <div class="hotItem" v-for="i in 3" :class="{ n1: i == 2, n2: i == 3 }"></div>
        </div>
        {{ $t('Me_calendar_more') }}
      </div>
    </div>
    <div class="hotChart">
      <div class="hotItems">
        <div class="itemBox" v-for="i in weekCount">
          <div class="hotItem" v-for="j in 7" :class="[getBgClass(i, j)]"></div>
        </div>
      </div>
      <div class="hotMonths">
        <span v-for="m in myMonths">{{ m }}</span>
      </div>
    </div>
    <template v-if="userInfo.membership_id || userInfo.membership_id_traffic">
      <div class="uline"></div>
      <h6 style="margin-bottom: 14px">{{ $t('Me_membercard_title') }}</h6>
      <div class="shipInfos">
        <div class="shipInfo" v-if="userInfo.membership_id">
          <div class="shipFlex">
            <div class="flex-center">
              <div class="shipName">{{ homeInfo.memberName }}</div>
              <div
                class="shipAuto"
                v-if="
                  userStateInfo.membership_type == 'pro' &&
                  (userStateInfo.autorenew_status_android || userStateInfo.autorenew_status_ios)
                "
              >
                ({{ getPackageType() }})
              </div>
            </div>
            <div class="date" v-if="userStateInfo.membership_type != 'starter'">
              {{
                getTranslateStr(
                  'Web_me_membercard_exspire',
                  '20240327',
                  getUserDateStr(userInfo.expire_time),
                )
              }}
            </div>
          </div>
          <div class="barBox">
            <div
              class="barpro"
              :style="{ width: getPerNum(userInfo.seconds_left, userInfo.seconds_total) + '%' }"
            ></div>
          </div>
          <div class="shipFlex">
            <div class="timeMin">
              {{
                getTranslateStr(
                  'Me_membercard_subtitle_lefttime_left',
                  '20240327',
                  getMinNum(userInfo.seconds_left),
                )
              }}
              <span class="total">{{
                getTranslateStr(
                  'Me_membercard_subtitle_lefttime_total',
                  '20240327',
                  getMinNum(userInfo.seconds_total),
                )
              }}</span>
            </div>
            <div class="date" v-if="userStateInfo.membership_type != 'starter'">
              {{
                getTranslateStr(
                  'Me_membercard_subtitle_nextreset',
                  '20240327',
                  getUserDateStr(userInfo.reset_time),
                )
              }}
            </div>
          </div>
        </div>
        <div class="shipInfo" v-if="userInfo.membership_id_traffic">
          <div class="shipFlex">
            <div class="flex-center">
              <div class="shipName">{{ homeInfo.packageInfo.traffic }}</div>
            </div>
            <div class="date">
              {{
                getTranslateStr(
                  'Web_me_membercard_exspire',
                  '20240327',
                  getUserDateStr(userInfo.expire_time_traffic),
                )
              }}
            </div>
          </div>
          <div class="barBox">
            <div
              class="barpro"
              :style="{
                width:
                  +getPerNum(userInfo.seconds_left_traffic, userInfo.seconds_total_traffic) + '%',
              }"
            ></div>
          </div>
          <div class="shipFlex">
            <div class="timeMin">
              {{
                getTranslateStr(
                  'Me_membercard_quota_lefttime_left',
                  '20240327',
                  getMinNum(userInfo.seconds_left_traffic),
                )
              }}
              <span class="total">{{
                getTranslateStr(
                  'Me_membercard_quota_lefttime_total',
                  '20240327',
                  getMinNum(userInfo.seconds_total_traffic),
                )
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="uline"></div>
    <h6 style="margin-bottom: 16px">{{ $t('Web_setting') }}</h6>
    <div class="h7" style="margin-bottom: 8px">{{ $t('Web_setting_language') }}</div>
    <el-select v-model="selectLang" placeholder="please select" style="width: 448px">
      <el-option v-for="item in languageList" :key="item.id" :label="item.name" :value="item.id">
      </el-option>
    </el-select>
    <div class="formSmallBtns" style="margin-top: 22px; padding-bottom: 20px">
      <div
        class="smallComBtn"
        :class="{ active: mySelectLang != selectLang, disable: mySelectLang == selectLang }"
        @click="saveLang(true)"
      >
        {{ $t('Web_setting_button_left') }}
      </div>
      <div
        class="smallComBtn white"
        :class="{
          active: mySelectLang != selectLang,
          'cursor-desabled': mySelectLang == selectLang,
        }"
        @click="saveLang(false)"
      >
        {{ $t('Web_setting_button_right') }}
      </div>
    </div>
    <div class="uline sline"></div>
    <h6 style="margin-bottom: 16px">{{ $t('Web_setting_share_ideas') }}</h6>
    <div class="share-box">
      <div class="share-btn" @click="skipToFrill">
        {{ $t('Web_setting_share_ideas_button') }}
        <div class="circle-red" v-if="redDotShow"></div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations, mapGetters } from 'vuex';
import langList from '@/data/sys_language.js';
import i18n, { setLocale } from '../../../language';
import Cookies from 'js-cookie';
import { getFrillUrl, FRILL_DATA } from '@/util/frill';

export default {
  name: 'UserInfo',
  data() {
    return {
      userInfo: this.$root.userInfo,
      userStateInfo: this.$root.userStateInfo,
      totalList: [
        { name: this.getTranslateStr('Me_calendar_day', '20240327', ''), key: 'total_days' },
        { name: this.getTranslateStr('Me_calendar_files', '20240327', ''), key: 'total_files' },
        { name: this.getTranslateStr('Me_calendar_hours', '20240327', ''), key: 'total_duration' },
        { name: this.getTranslateStr('Me_calendar_streak', '20240327', ''), key: 'max_days' },
        { name: this.getTranslateStr('Me_calendar_maxrecords', '20240327', ''), key: 'max_files' },
        { name: this.getTranslateStr('Me_calendar_maxhours', '20240327', ''), key: 'max_duration' },
      ],
      homeInfo: this.$parent.$parent,
      statInfo: {},
      monthTitle: [
        this.$t('Me_calendar_jan'),
        this.$t('Me_calendar_feb'),
        this.$t('Me_calendar_mar'),
        this.$t('Me_calendar_apr'),
        this.$t('Me_calendar_may'),
        this.$t('Me_calendar_jun'),
        this.$t('Me_calendar_jul'),
        this.$t('Me_calendar_aug'),
        this.$t('Me_calendar_sep'),
        this.$t('Me_calendar_oct'),
        this.$t('Me_calendar_nov'),
        this.$t('Me_calendar_dec'),
      ],
      myMonths: [],
      selectLang: 'en',
      mySelectLang: '',
      languageList: langList,
      weekCount: 52, //显示最近52周数据
      isEmailShow: false,
    };
  },
  computed: {
    ...mapState({
      authInfo: (state) => state.auth.authInfo, // 登录信息
      currentLoginType: (state) => state.auth.currentLoginType, // 登录类型
      frillSsoToken: (state) => state.sso.frillSsoToken, // Frill ssoToken
      locale: (state) => state.auth.locale,
    }),
    ...mapGetters('auth', ['redDotShow']),
  },
  mounted() {
    let month = new Date().getMonth() + 1,
      startM = month - 12,
      m1 = [],
      m2 = [];
    if (startM < 0) {
      m1 = this.monthTitle.slice(0, month);
      m2 = this.monthTitle.slice(12 + startM, 12);
    } else {
      m1 = this.monthTitle.slice(startM, month);
    }
    this.myMonths = [...m2, ...m1];
    this.getUserStat();

    this.mySelectLang = this.selectLang =
      window.localStorage.getItem('pweblang') || this.$i18n.locale || 'en_US';

    this.initEmailShow();
  },
  methods: {
    ...mapMutations('auth', ['AUTH_SET_REDDOTSHOW', 'AUTH_SET_LOCALE']),
    async initEmailShow() {
      try {
        if (this.currentLoginType === 'email') {
          // 如未存在登录态，邮箱登录进来的则直接返回
          this.isEmailShow = true;
          return;
        }
        const data = await this.reqGetInfo('/auth/sso-list');
        const isSSOAcount = Array.isArray(data) && data.some((item) => item.is_account);
        this.isEmailShow = !isSSOAcount;
      } catch (error) {
        console.error('getSSOList', error);
      }
    },
    getHourStr(duration) {
      let totalhours = 0;
      if (duration > 0) {
        totalhours = duration / (60 * 60 * 1000);
        if (String(totalhours).includes('.')) {
          totalhours = totalhours.toFixed(1);
        }
      }
      return totalhours;
    },
    getPackageType() {
      let { expire_time, start_time } = this.userInfo;
      let mus = expire_time - start_time;
      let days = Math.ceil(mus / (60 * 60 * 24));
      return this.$t(
        days >= 300
          ? 'Membership_moredetails_ongoing_pro_annual'
          : 'Membership_moredetails_ongoing_pro_monthly',
      );
    },
    getUserStat() {
      //当前月的填充：计算当前月剩余天数，最后一天星期几，计算最终要填充的天数。查询的时候减去填充的天数
      let endTime = new Date().getTime(),
        startTime = new Date(),
        totalDays = Date.getDaysInMonth(startTime.getFullYear(), startTime.getMonth()),
        today = startTime.getDate();
      let lastDay = new Date(),
        leftDay = totalDays - today;
      lastDay.addDays(leftDay);
      let fillDay = (leftDay > 0 ? leftDay - 7 : leftDay) + (6 - lastDay.getDay());
      startTime.addDays(-this.weekCount * 7 + fillDay);
      this.reqGetInfo(
        '/user/stat/file?diff_time=0&start_time=' + startTime.getTime() + '&end_time=' + endTime,
      ).then((data) => {
        let myData = data.data_stat,
          { group_result } = myData,
          groups = {};
        if (group_result.length > 0) {
          let nowTime = new Date(new Date().Format1('yyyy-MM-dd')).getTime(),
            ts = this.weekCount * 7 - fillDay - 1;
          group_result.forEach((group) => {
            let d = group.day,
              days = group.count;
            let plusd = (nowTime - new Date(d).getTime()) / (24 * 3600 * 1000);
            groups[String(ts - plusd)] = days > 2 ? 'n2' : 'n1';
          });
        }
        myData.groups = groups;
        this.statInfo = myData;
      });
    },
    getBgClass(col, row) {
      let groups = this.statInfo.groups;
      let key = (col - 1) * 7 + (row - 1);
      try {
        return groups[String(key)];
      } catch (e) {}
      return '';
    },
    getUserDateStr(date) {
      return this.getDateStr(date * 1000, 'yyyy-MM-dd');
    },
    async saveLang(save) {
      if (this.mySelectLang != this.selectLang) {
        if (save) {
          // 使用 setLocale 切换语言
          await setLocale(this.selectLang);
          this.setGALogEvent('web_me_language');
          // window.localStorage.setItem('pweblang', this.selectLang);
          this.mySelectLang = this.selectLang;
          // this.$i18n.locale = this.selectLang;
          this.AUTH_SET_LOCALE(this.selectLang);
          Cookies.remove('plaudfamoustxt');
          window.location.reload();
        } else {
          this.selectLang = this.mySelectLang;
        }
      }
    },
    async skipToFrill() {
      this.setGALogEvent('web_shareideas_click');
      // let isJP = this.locale === 'ja_JP'
      let frillURL = FRILL_DATA.ideas;
      // let url = isJP? FRILL_DATA.line : getFrillUrl(frillURL, this.frillSsoToken)
      let url = getFrillUrl(frillURL, this.frillSsoToken);
      if (this.redDotShow) {
        const data = {
          name: this.userInfo.email,
          data: { frill: false },
        };
        this.AUTH_SET_REDDOTSHOW(data);
        // await this.updateRedDotShow()
      }
      window.open(url);
    },
    // 将小红点显示值设为false
    // async updateRedDotShow(){
    //   try {
    //     const data = await this.reqPostInfo('', {flag: false});
    //     // const data = await this.reqGetInfo('');
    //     // console.log('frill/red-dot-show:', data)
    //   } catch (error) {
    //     console.error('frill/red-dot-show', error);
    //   }
    // }
  },
};
</script>
<style scoped lang="scss">
.uline.sline {
  margin-top: 4px;
}
.share-box {
  display: flex;
  position: relative;
  .share-btn {
    position: relative;
    min-width: 143px;
    padding: 9px 20px;
    margin-bottom: 16px;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    border-radius: 6px;
    background: #ffff;
    border: 1px solid #e4e7ec;
    cursor: pointer;
    &:hover {
      background: rgba(228, 231, 236, 0.65);
    }
    &:active {
      background: #e4e7ec;
    }
  }
  .circle-red {
    position: absolute;
    top: 8px;
    right: 8px;
  }
}
</style>
