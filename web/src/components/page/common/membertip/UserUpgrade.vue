<template>
  <MemberModal
    :visible="dialogStatus"
    :links="[{ url: 'https://www.plaud.ai/pages/app-faq?_pos=1&_psq=FAQ&_ss=e&_v=1.0', text: $t('member_dict_faq') }, { url: 'https://app.plaud.ai/user-agreement', text: $t('member_dict_user_agreement') }]"
    width="480px"
    :confirmButtonText="$t('member_dict_got_it')"
    @close="handlerVisible(false)"
    @confirm="handlerVisible(false)"
    :title="!proUser && infoFlag > 1 ? $t('member_pro_upgrade_modal_title'):''"
    backgroundDecorator
  >
    <template v-slot:title  v-if="infoFlag==1">
      <MemberModalTimeUnit :recordingTime="getTimeName(getTimeStr(sendInfo.duration, false))" :remainingTime="getTimeName(getTimeStr(sendInfo.left, false, false))" />
    </template>
    <template v-slot:content>
      <!-- pro用户的提示语 -->
      <p v-if="proUser">{{$t('member_transcription_alert_text')}}</p>
      <MemberModalUpgradeUnit v-else
        :title="$t('member_pro_transcription_modal_upgrade_title')"
        :subtitle="$t('member_pro_transcription_modal_upgrade_subtitle')"
        :features="[
                { theme: 'orange', active: infoFlag==1 || infoFlag==4, icon: 'icon-icon_benefit_transcription', text: $t('member_pro_transcription_modal_upgrade_transcription') },
                { theme: 'purple', active: infoFlag==2, icon: 'icon-icon_benefit_template', text: $t('Guide_step4_column_2_pro') },
                { theme: 'purple', active: infoFlag==3, icon: 'icon-icon_benefit_customize', text: $t('member_pro_transcription_modal_upgrade_prompt') },
                { theme: 'purple', active: infoFlag==5, icon: 'icon-ai', text: $t('member_pro_transcription_modal_upgrade_askai') },
              ]"
        :desc="$t('member_pro_upgrade_modal_upgrade_notice')"
      />
    </template>
  </MemberModal>

</template>
<script>
import MemberModal from '@/components/common/modal/MemberModal.vue';
import MemberModalTimeUnit from '@/components/common/modal/MemberModalTimeUnit.vue';
import MemberModalUpgradeUnit from '@/components/common/modal/MemberModalUpgradeUnit.vue';
export default {
  components: {MemberModal,MemberModalTimeUnit,MemberModalUpgradeUnit},
  data(){
    return {
      dialogStatus:false,
      infoFlag:0,//1:时长不够 2:没有系统模版权限 3:没有自定义模版权限 4:升级pro
      sendInfo:{},
      proUser:false,
    }
  },
  methods:{
    handlerVisible(status,info={}){
      this.dialogStatus = status;
      if(status){
         let memberType = this.$root.userStateInfo.membership_type;
         this.proUser = ['backer', 'pro','unlimited'].includes(memberType);
         this.infoFlag = info.flag;
         this.sendInfo = info;
      }
      else{
        this.infoFlag = 0;
        this.sendInfo = {};
        this.$emit('close');
      }
    }
  }
}
</script>
