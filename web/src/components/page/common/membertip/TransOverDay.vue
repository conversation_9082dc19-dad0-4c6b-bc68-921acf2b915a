<template>
<MyDialog  width="480px" :hasTitle="false" :visible="dialogStatus">
  <template v-slot:content>
    <div class="transOverWrap">
      <img class="faceImg" src="static/t_face.png">
      <div class="desc1">{{$t('web_trans_over24_title')}}</div>
      <div class="desc2" v-html="getTranslateStr('web_trans_over24_content','%s',24)"></div>
      <div class="desc3">{{$t('web_trans_over24_desc')}}</div>
      <ul>
        <li>{{getTranslateStr('member_dict_recording_hastrans','%s',getTimeName(getTimeStr(totalTime, false,false)))}}</li>
        <li>{{getTranslateStr('member_dict_recording_currentaudio','%s','')}}<span class="red">{{getTimeName(getTimeStr(fileTime, false,false))}}</span></li>
        <li>{{getTranslateStr('member_dict_recording_remaintrans','%s',getTimeName(getTimeStr(limitTime - totalTime, false,false)))}}</li>
      </ul>
      <div class="commonBtn" @click="handlerVisible(false)">{{$t('Import_audio_web_limitsize_btn')}}</div>
    </div>
  </template>
</MyDialog>
</template>
<script>
import MyDialog from "../../../common/MyDialog.vue";
export default {
  name:'TransOverDay',
  components:{MyDialog},
  data(){
    return {
      dialogStatus:false,
      fileTime:0,
      totalTime:0,
      limitTime:100*60*60
    }
  },
  methods:{
    handlerVisible(status,duration,total_seconds){
      if(status){
         if(duration + total_seconds > this.limitTime){
           this.fileTime = duration;
           this.totalTime = total_seconds;
           this.dialogStatus = status;
           return false;
         }
         else{
           status = false;
         }
      }
      this.dialogStatus = status;
      return true;
    }
  }
}
</script>
<style scoped lang="scss">
.transOverWrap{
  padding:12px;
  .faceImg{
    height: 32px;
    margin-top: 28px;
  }
  .desc1{
    color:#1F1F1F;
    font-size: 26px;
    font-style: normal;
    font-weight: 790;
    line-height: 28px;
    letter-spacing: -0.24px;
    margin: 5px 0px;
  }
  .desc2{
    color:#858C9B;
    font-size: 14px;
    word-break: keep-all;
  }
  .desc3{
    color:#1F1F1F;
    font-size: 14px;
    font-weight: 656;
    margin-top: 24px;
    margin-bottom: 5px;
  }
  ul{
    list-style: disc;
    list-style-position: inside;
    margin-bottom: 40px;
    padding-left: 5px;
    li{
      color:#858C9B;
      font-size: 14px;
      .red{
        color:#FF3B30;
      }
    }
  }
}

</style>
