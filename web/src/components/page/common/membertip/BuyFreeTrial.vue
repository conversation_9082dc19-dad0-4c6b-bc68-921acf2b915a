<!--free trial支付成功的弹框-->
<template>
  <transition name="fade">
    <div class="modal-overlay" v-if="dialogStatus">
      <div class="pop-wrap" :class="[memberType]">
        <div class="pop-box">
          <img src="static/good.gif">
        </div>
        <div class="pop-title">{{$t('Membership_quotaed_title')}}</div>
        <div class="desc-item">{{$t('web_free_trial_alert_purchase_success_tips_one')}}</div>
        <div class="desc-item date"  v-html="getTranslateStr('web_free_trial_alert_purchase_success_tips_two','%s',getDateStr($root.userInfo.expire_time*1000,'yyyy-MM-dd'))"></div>
        <div class="desc-item long-desc" v-html="getTranslateStr('web_free_trial_alert_purchase_success_tips_three')"></div>
        <div class="commonBtn" @click="goToDetail(1)">{{$t('Membership_subscribe_success_button')}}</div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: 'BuyFreeTrial',
  data() {
    return {
      dialogStatus: false,
      memberType:'pro',
      expireDate:''
    }
  },
  methods: {
    handlerVisible(status, info) {
      this.dialogStatus = status;
      if (!status) {
        this.$emit('close',false);
      }
      else{
        this.memberType = this.$root.userStateInfo.membership_type;
      }
    },
    goToDetail(flag){
      this.$emit('close',false,flag);
      this.dialogStatus = false;
    }
  }
}
</script>
<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.1s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.pop-wrap {
  padding: 54px 24px 24px 24px;
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;
  border-radius: 20px;
  width:340px;
  position: absolute;
  left:50%;
  top:50%;
  transform: translate(-50%,-50%);
  .pop-box{
    height: 94px;
    width:183px;
    background-image: url('../../../../../public/static/good_bg.png');
    background-size: contain;
    background-position: top;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left:50%;
    top:0%;
    transform: translate(-50%,-60%);
    img{
      width:80px;
    }
  }
  .pop-title{
    text-align: center;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 32px;
    margin-bottom: 12px;
  }
  .desc-item{
    font-size:15px;
    text-align: center;
    color:#475467;
    margin-bottom: 12px;
    &.date{
      color:#1F1F1F;
    }
  }
  .commonBtn{
    margin-top: 20px;
  }
  &.pro{
    background-color: #fff;
    background-image: url('../../../../../public/static/dialog_bg_membership_free_success.png');
  }
  &.unlimited{
    background: #1F1F1F;
    .pop-title{
      background: linear-gradient(96deg, #FFE58E 1.86%, #FFF5E0 52.77%, #C49E20 98.24%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .desc-item{
      color:#fff;
      &.date{
        color:#fff;
      }
      &.long-desc{
        color:rgba(255, 255, 255, 0.70);
      }
    }
    .commonBtn{
      border-radius: 26px;
      background: linear-gradient(103deg, #775514 0%, #FFE9BE 50%, #5B3F0C 100%);
      color:#1F1F1F;
    }
  }
}

</style>
