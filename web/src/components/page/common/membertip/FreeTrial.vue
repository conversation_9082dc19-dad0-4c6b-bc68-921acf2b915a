<template>
  <transition name="fade">
    <div class="modal-overlay" v-if="dialogStatus">
      <div class="pop-wrap" style="background-image: url('static/dialog_bg_membership_free.png')">
        <img src="static/upgrade_close.png" class="upgrade-close" @click="handlerVisible(false)" />
        <div class="pop-title" :class="{ trans: sendInfo.flag === 1 }">
          {{ $t('web_free_trial_alert_title') }}
        </div>
        <div class="transBox" v-if="sendInfo.flag === 1">
          <div class="trans-time">{{ $t('member_pro_transcription_modal_title') }}</div>
          <div class="trans-time">
            {{ getTranslateStr('member_dict_recording_duration', '%s', '') }}
            <span class="b">{{ getTimeName(getTimeStr(sendInfo.duration, false)) }}</span>
          </div>
          <div class="trans-time">
            {{ $t('member_dict_transcription_time') }}
            <span class="b">{{ getTimeName(getTimeStr(sendInfo.left, false, false)) }}</span>
          </div>
        </div>
        <div class="cont-list">
          <div class="cont-item" :class="[info.type]" v-for="info in contentList">
            <div class="cont-item-title">
              <span class="liner-txt">{{ info.title }}</span>
            </div>
            <!--            <div class="cont-item-desc">{{info.desc}}</div>-->
            <ul>
              <li v-for="tip in info.tipList">
                <img :src="info.icon" /><span
                  :class="{ 'active-liner': tip.active === activeIndex }"
                  >{{ tip.content }}</span
                >
              </li>
            </ul>
          </div>
        </div>
        <div class="flex-center">
          <div class="commonBtn" @click="goToDetail(1)">
            {{ $t('web_free_trial_alert_bottom_button_title') }}
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: 'FreeTrial',
  data() {
    return {
      dialogStatus: false,
      activeIndex: -1,
      sendInfo: {},
      contentList: [
        {
          type: 'pro',
          title: this.$t('Membership_pro'),
          desc: this.$t('web_memeber_center_ypro_tips'),
          icon: 'static/icon_check_3.png',
          tipList: [
            {
              active: 0,
              content:
                this.$t('Membership_introduction_tabone_ai_feature1_pro') +
                ' ' +
                this.$t('Membership_introduction_tabone_ai_feature1'),
            },
            { active: 2, content: '30+ ' + this.$t('web_memeber_center_professional') },
            { active: 3, content: this.$t('Membership_introduction_tabtwo_2') },
            { active: 5, content: this.$t('Filedetail_editor_AskAI') }, //,isNew:true
            { content: this.$t('Membership_subscribe_pro_overview_decription') },
            { content: this.$t('Membership_subscribe_unlimited_decription_3') },
          ],
        },
        {
          type: 'unlimited',
          title: this.$t('Me_membercard_unlimited'),
          desc: this.$t('Membership_subscribe_unlimited_decription_1'),
          icon: 'static/icon_check_4.png',
          tipList: [
            { active: 0, content: this.$t('Membership_subscribe_unlimited_decription_2') },
            { content: this.$t('Membership_subscribe_unlimited_decription_3') },
            { active: 2, content: '30+ ' + this.$t('web_memeber_center_professional') },
            { active: 3, content: this.$t('Membership_introduction_tabtwo_2') },
            { active: 5, content: this.$t('Filedetail_editor_AskAI') }, //,isNew:true
            { content: this.$t('Membership_subscribe_pro_overview_decription') },
          ],
        },
      ],
    };
  },
  methods: {
    handlerVisible(status, info) {
      this.dialogStatus = status;
      if (!status) {
        this.$emit('close', false, 3);
        this.sendInfo = {};
      } else {
        if (info.flag === 1 || info.flag === 4) {
          //trans
          this.activeIndex = 0;
        } else {
          this.activeIndex = info.flag;
        }
        this.sendInfo = info;
      }
    },
    goToDetail(flag) {
      this.$emit('close', false, flag);
      this.dialogStatus = false;
    },
  },
};
</script>
<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.1s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.pop-wrap {
  padding: 30px 32px 22px 32px;
  background-color: #f2f4f7;
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;
  border-radius: 20px;
  width: 723px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  .upgrade-close {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 30px;
    cursor: pointer;
  }
  .pop-title {
    text-align: center;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.24px;
    margin-bottom: 23px;
    &.trans {
      margin-bottom: 12px;
    }
  }
  .transBox {
    margin-bottom: 15px;
  }
  .trans-time {
    color: #1f1f1f;
    text-align: center;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 2px;
    .b {
      font-weight: 700;
    }
  }
  .cont-list {
    display: flex;
    margin-bottom: 24px;
    .cont-item {
      flex: 1;
      border-radius: 24px;
      padding: 16px;
      background: rgba(255, 255, 255, 0.7);
      box-shadow:
        0px 1px 1px -0.5px rgba(0, 0, 0, 0.04),
        0px 3px 3px -1.5px rgba(0, 0, 0, 0.04),
        0px 6px 6px -3px rgba(0, 0, 0, 0.04),
        0px 32px 32px -16px rgba(0, 0, 0, 0.04),
        0px 56px 56px -28px var(--elevation-shadow, rgba(0, 0, 0, 0.04));
      .cont-item-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 16px;
      }
      .cont-item-desc {
        color: #475467;
        font-size: 15px;
        font-weight: 590;
      }
      &.pro {
        border: 1px solid #a094ff;
        .liner-txt,
        .active-liner {
          background: linear-gradient(90deg, #6675ff 0%, #fe3ba6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          color: transparent;
        }
        .active-liner {
          font-weight: 590;
        }
      }
      &.unlimited {
        border: 1px solid #a98a57;
        .liner-txt {
          background: linear-gradient(102deg, #f3d59a 5.3%, #a0834c 48.02%, #b18648 96.32%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          color: transparent;
        }
        .active-liner {
          background: var(
            --Unlimited-Title,
            linear-gradient(102deg, #bda16c 3.44%, #b18648 96.32%)
          );
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: 590;
        }
      }
      &:first-child {
        margin-right: 22px;
      }
    }
    ul {
      list-style: none;
      li {
        display: flex;
        align-items: center;
        color: #475467;
        font-size: 14px;
        font-weight: 400;
        margin-top: 12px;
        img {
          width: 16px;
          margin-right: 6px;
        }
      }
    }
  }
  .commonBtn {
    width: 330px;
    box-shadow:
      0px 1px 1px -0.5px var(--elevation-shadow, rgba(0, 0, 0, 0.04)),
      0px 3px 3px -1.5px var(--elevation-shadow, rgba(0, 0, 0, 0.04)),
      0px 3px 6px -2px var(--color-white-64, rgba(255, 255, 255, 0.64)) inset,
      0px 0px 8px -2px var(--color-white-48, rgba(255, 255, 255, 0.48)) inset;
  }
}
</style>
