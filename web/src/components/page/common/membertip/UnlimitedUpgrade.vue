<template>
  <transition name="fade">
    <div class="modal-overlay" v-if="dialogStatus">
      <div class="pop-wrap">
        <img src="static/upgrade_close.png" class="upgrade-close" @click="handlerVisible(false)">
        <div class="unlimited-title linearTxt">
          {{$t('Unlockunlimited_title')}}
        </div>
        <template v-if="sendInfo.flag === 1">
          <div class="trans-time">{{$t('member_pro_transcription_modal_title')}}</div>
          <div class="trans-time">{{getTranslateStr('member_dict_recording_duration','%s','')}} <span class="red">{{getTimeName(getTimeStr(sendInfo.duration, false))}}</span> </div>
          <div class="trans-time">{{$t('member_dict_transcription_time')}} {{getTimeName(getTimeStr(sendInfo.left, false, false))}}</div>
        </template>
        <div class="cont-list">
          <div class="cont-title linearTxt">{{$t('Membership_subscribe_unlimited_year')}} </div>
          <ul>
            <li v-for="(key,index) in keyList" :class="{'linearTxt':index===activeIndex}">
              <img :src="index === activeIndex ? 'static/icon_check_1.png':'static/icon_check.png'">
              {{$t(key)}}
            </li>
          </ul>
        </div>
        <div class="upgrade-btn" data-pld="member-entry-unlimited-upgrade" @click="goToDetail(1)">{{$t('Unlockunlimited_freetrail_button')}}</div>
        <div class="upgrade-bottom">
          <span @click="goToDetail(2)">{{$t('Me_ms_upgrade_to_unlock_alert_footer_left')}}</span>
          <div class="bottom-right">
            <a href="https://www.plaud.ai/pages/app-faq?_pos=1&_psq=FAQ&_ss=e&_v=1.0" target="_blank">{{$t('member_dict_faq')}}</a>
            <div class="line"></div>
            <a href="https://app.plaud.ai/user-agreement" target="_blank">{{$t('member_dict_user_agreement')}}</a>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: 'UnlimitedUpgrade',
  data() {
    return {
      dialogStatus: false,
      keyList:['Unlockunlimited_freetrail_transcription','Unlockunlimited_freetrail_industryglossary','Unlockunlimited_freetrail_professionaltemp','Unlockunlimited_freetrail_coustomltemp','Unlockunlimited_freetrail_askai','Unlockunlimited_freetrail_overview'],
      activeIndex:-1,
      sendInfo:{}
    }
  },
  methods: {
    handlerVisible(status, info) {
      this.dialogStatus = status;
      if (status) {
        if(info.flag === 1 || info.flag === 4){ //trans
          this.activeIndex = 0;
        }
        else if(info.flag === 2){this.activeIndex = 2;} //专业模版
        else if(info.flag === 3){this.activeIndex = 3;} //自定义模版
        else if(info.flag === 5){this.activeIndex = 4;}//askai
        this.sendInfo = info;
      }
      else{
        this.sendInfo = {};
        this.activeIndex = -1;
        this.$emit('close',false,3);
      }
    },
    goToDetail(flag){
      this.$emit('close',false,flag);
      this.dialogStatus = false;
    }
  }
}
</script>
<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.01);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.1s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.pop-wrap {
  padding: 46px 32px 22px 32px;
  background: #050506;
  border-radius: 20px;
  width:480px;
  position: absolute;
  left:50%;
  top:50%;
  transform: translate(-50%,-50%);
  .upgrade-close{
    position: absolute;
    right:10px;
    top:10px;
    width:30px;
    cursor: pointer;
  }
  .unlimited-title{
    text-align: center;
    font-size: 28px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.24px;
    padding-bottom: 10px;
  }
  .linearTxt{
    background: linear-gradient(96deg, #FFE58E 1.86%, #FFF5E0 52.77%, #C49E20 98.24%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }
  .trans-time{
    color: rgba(255, 255, 255, 0.50);
    text-align: center;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 5px;
    .red{
      color:rgba(241, 67, 73, 0.70);
    }
  }
  .cont-list{
    margin:24px 0px;
    padding:16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.10);
    box-shadow: 0px 10px 50px 0px rgba(139, 53, 19, 0.30);
    position: relative;
    .giftImg{
      width:90px;
      position: absolute;
      top:-25px;
      right:-25px;
    }
    .cont-bg{
      opacity: 0.5;
      background: linear-gradient(180deg, #89761B 0%, #B97A19 50%, #FF943D 100%);
      filter: blur(100px);
      width: 303px;
      height: 296px;
      position: absolute;
      top:26px;
      left:26px;
    }
    .cont-title{
      color:#fff;
      font-size: 18px;
      font-style: normal;
      font-weight: 590;
      line-height: 25px;
      display: flex;
      align-items: center;
      .title-sign{
        display: inline-block;
        height: 28px;
        padding: 0px 8px;
        background: rgba(255, 255, 255, 0.20);
        border-radius: 6px;
        color:#fff;
        font-size: 14px;
        font-weight: 590;
        margin-left: 8px;
      }
    }
    ul{
      list-style: none;
      border-top: 0.33px solid rgba(228, 231, 236, 0.50);
      padding-top: 4px;
      margin-top: 12px;
      li{
        display: flex;
        align-items: center;
        color:#fff;
        font-size: 15px;
        font-weight: 400;
        margin-top: 12px;
        img{
          width:18px;
          margin-right: 6px;
        }
      }
    }
  }
  .upgrade-btn{
    background: linear-gradient(103deg, #775514 0%, #FFE9BE 50%, #5B3F0C 100%);
    border-radius: 14px;
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: center;
    color:#1F1F1F;
    font-size: 17px;
    font-weight: 590;
    cursor: pointer;
  }

  .upgrade-bottom{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    color:#858C9B;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    span,a{
      cursor: pointer;
      text-underline: none;
      text-decoration: none;
    }
    .bottom-right{
      display: flex;
      align-items: center;
      .line{
        height: 10px;
        width:1px;
        background: #858C9B;
        margin:0px 10px;
      }
    }
  }
}

</style>
