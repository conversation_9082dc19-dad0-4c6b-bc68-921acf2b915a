<template>
  <MyDialog
    :title="$t('Filelist_selected_rename')"
    width="417px"
    :visible="dialogStatus"
    @close="dialogStatus = false"
  >
    <template v-slot:content>
      <div>
        <textarea v-model="filename" class="txt" @input="errorMsg = ''"></textarea>
        <div class="formError" style="margin-bottom: 5px">{{ errorMsg }}</div>
        <div class="flex-middle btns">
          <div class="commonBtn gray" @click="dialogStatus = false">
            {{ $t('Filelist_selected_rename_button_left') }}
          </div>
          <div class="commonBtn" @click="save">
            {{ $t('Filelist_selected_rename_button_right') }}
          </div>
        </div>
        <Loading :status="isLoading" />
      </div>
    </template>
  </MyDialog>
</template>

<script>
import MyDialog from '../../common/MyDialog.vue';
export default {
  name: 'EditName',
  components: { MyDialog },
  data() {
    return {
      dialogStatus: false,
      filename: '',
      errorMsg: '',
    };
  },
  methods: {
    handlerVisible(status, filename) {
      this.filename = filename;
      this.dialogStatus = status;
      this.errorMsg = '';
    },
    save() {
      this.setGALogEvent('web_edit_filename_save');
      let params = { filename: this.filename };
      if (params.filename == '') {
        this.errorMsg = this.$t('Filelist_selected_rename_noname');
        return false;
      }
      // if(params.filename.length > 80){
      //     this.errorMsg = this.$t('Filelist_selected_rename_toolong');
      //     return false;
      // }
      this.errorMsg = '';
      this.isLoading = true;
      let fileInfo = this.$parent.$parent.info;
      this.reqPatchInfo('/file/' + fileInfo.id, params).then((data) => {
        this.isLoading = false;
        this.$set(fileInfo, 'filename', params.filename);
        this.dialogStatus = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.txt {
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  height: 140px;
  outline: none;
  font-size: 16px;
  color: #000000;
  line-height: 24px;
  resize: none;
  width: 100%;
  margin-top: 9px;
  box-sizing: border-box;
  padding: 16px;
}
.btns {
  margin-bottom: 9px;
  margin-top: 10px;
  .commonBtn {
    flex: 1;
    &:first-child {
      margin-right: 15px;
    }
  }
}
</style>
