<template>
  <MyDialog :title="title" width="375px" :visible="dialogStatus" @close="dialogStatus = false">
    <template v-slot:content>
      <div>
        <div class="formTag">
          <div class="formItem" style="margin-bottom: 0px">
            <label>{{ $t('Folder_info_name') }}</label>
            <input
              type="text"
              class="ipt"
              v-model="formInfo.name"
              @input="changeInput"
              :placeholder="$t('Folder_create_tips')"
            />
          </div>
          <div class="formError">{{ errorMsg }}</div>
          <div class="formItem">
            <label>{{ $t('Folder_info_color') }}</label>
            <div class="tagBox">
              <div
                class="tagWrap"
                v-for="color in tagColorList"
                :class="{ active: formInfo.color == color }"
                @click="changeColor(color)"
              >
                <div class="tag" :style="{ 'background-color': color }"></div>
              </div>
            </div>
          </div>
          <div class="formItem">
            <label>{{ $t('Folder_info_icon') }}</label>
            <div class="iconBox" @click="doChooseIcon()">
              <div class="myicon iconfont" v-html="getIconCode(formInfo.icon)"></div>
              <div class="el-icon-arrow-right"></div>
            </div>
          </div>
        </div>
        <div class="formBtn">
          <div class="commonBtn" @click="doSave()">
            {{ formInfo.id ? $t('Folder_edit_button') : $t('Folder_create_button') }}
          </div>
          <div class="commonBtn del" @click="doRemove" v-if="formInfo.id">
            {{ $t('Folder_delete_button_left') }}
          </div>
        </div>
        <ChooseIcon ref="chooseIcon" @okIcon="ckIcon" />
        <Loading :status="isLoading" />
      </div>
    </template>
  </MyDialog>
</template>
<script>
import MyDialog from '../../common/MyDialog.vue';
import ChooseIcon from './ChooseIcon.vue';
import { tagColorList, tagIconList } from '@/data/TagStyle';
export default {
  components: { MyDialog, ChooseIcon },
  data() {
    return {
      dialogStatus: false,
      title: '',
      tagColorList: tagColorList,
      tagIconList: tagIconList,
      errorMsg: '',
      initForm: {
        name: '',
        color: tagColorList[0],
        icon: tagIconList[0],
      },
      hasInput: false,
      formInfo: { name: '', color: '', icon: '' },
      mySelect: [],
    };
  },
  methods: {
    handlerVisible(status, info, mySelect = []) {
      for (let k in this.initForm) {
        if (info[k]) {
          this.formInfo[k] = info[k];
        } else {
          this.formInfo[k] = this.initForm[k];
        }
      }
      if (info.id) {
        this.title = this.$t('Web_folder_edit_title');
        this.formInfo.id = info.id;
        this.setGALogEvent('web_edit_folder_sidebar');
      } else {
        this.title = this.$t('Folder_create_title');
        delete this.formInfo.id;
        this.setGALogEvent('web_create_folder');
      }
      this.errorMsg = '';
      this.mySelect = mySelect;
      this.dialogStatus = status;
      this.hasInput = false;
    },
    doChooseIcon() {
      this.setGALogEvent('web_create_folder_icon');
      this.$refs.chooseIcon.handlerVisible(true, this.formInfo.icon);
    },
    changeInput() {
      this.errorMsg = '';
      if (!this.hasInput) {
        this.hasInput = true;
        this.setGALogEvent('web_create_folder_type');
      }
    },
    doSave() {
      if (this.formInfo.name == '') {
        this.errorMsg = this.$t('Folder_noname');
        return false;
      } else if (this.formInfo.name.length > 255) {
        this.errorMsg = this.$t('Folder_toolong');
        return false;
      }
      if (this.formInfo.id) {
        this.isLoading = true;
        this.updateTagInfo(this.formInfo).then(
          (data) => {
            this.finshEvt();
          },
          (data) => {
            if (data.status === -2) {
              this.errorMsg = this.$t('Folder_exists');
            }
          },
        );
      } else {
        this.isLoading = true;
        this.createTagInfo(this.formInfo).then(
          (data) => {
            this.finshEvt();
          },
          (data) => {
            if (data.status === -2) {
              this.errorMsg = this.$t('Folder_exists');
            }
          },
        );
      }
    },
    finshEvt(isdel = false, count = 0) {
      if (isdel) {
        this.showSuccessMsg(this.$t('Folder_delete_toast'));
      } else {
        if (this.formInfo.id) {
          this.setGALogEvent('web_edit_folder_success');
        } else {
          this.setGALogEvent('web_create_folder_success');
        }
      }
      this.isLoading = false;
      this.errorMsg = '';
      this.$emit('ok', isdel, count, this.mySelect);
      this.dialogStatus = false;
    },
    doRemove() {
      this.setGALogEvent('web_delete_fullfolder');
      let count = this.$parent.getTagCount(this.formInfo.id);
      if (count > 0) {
        this.setConfirm({
          title: this.$t('Folder_delete'),
          msg: this.getTranslateStr('Folder_delete_content'),
          ok: () => {
            this.setGALogEvent('web_delete_fullfolder_success');
            this.isLoading = true;
            this.deleteTagInfo(this.formInfo.id).then(
              () => {
                this.finshEvt(this.formInfo.id, count);
              },
              (data) => {
                if (data.status === 404) {
                  // this.setMessage('filetag not found');
                }
              },
            );
          },
          cancel: () => {
            this.setGALogEvent('web_delete_fullfolder_fail');
          },
        });
      } else {
        this.setGALogEvent('web_delete_emptyfolder');
        this.isLoading = true;
        this.deleteTagInfo(this.formInfo.id).then(
          () => {
            this.finshEvt(this.formInfo.id);
          },
          (data) => {
            if (data.status === 404) {
              // this.setMessage('filetag not found');
            }
          },
        );
      }
    },
    changeColor(color) {
      this.$set(this.formInfo, 'color', color);
      if (this.formInfo.id) {
        this.setGALogEvent('web_folder_newcolor');
      } else {
        this.setGALogEvent('web_create_folder_color');
      }
    },
    ckIcon(icon) {
      this.$set(this.formInfo, 'icon', icon);
      this.setGALogEvent('web_folder_newicon');
    },
  },
};
</script>
<style scoped lang="scss">
.formTag {
  .formItem {
    background: #f7f8fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    height: 40px;
    margin-bottom: 12px;
    label {
      padding: 0px;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      width: 80px;
      flex-shrink: 0;
      margin-right: 10px;
    }
    .ipt {
      flex: 1;
      height: 30px;
      padding: 0px 5px;
      line-height: 30px;
      border: none;
      background: transparent;
    }
    .tagBox {
      display: flex;
      align-items: center;
      .tagWrap {
        border: 5px solid transparent;
        margin-right: 10px;
        cursor: pointer;
        border-radius: 7px;
        &.active {
          border-color: #e4e7ec;
        }
        .tag {
          width: 20px;
          height: 20px;
          border-radius: 4px;
        }
      }
    }
    .iconBox {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-end;
      cursor: pointer;
      .myicon {
        color: rgba(31, 31, 31, 1);
        font-size: 24px;
      }
      .el-icon-arrow-right {
        font-weight: 600;
        font-size: 14px;
        color: #98a2b3;
        margin-left: 7px;
      }
    }
  }
}
.formBtn {
  padding-top: 12px;
  .del {
    margin-top: 12px;
  }
}
</style>
