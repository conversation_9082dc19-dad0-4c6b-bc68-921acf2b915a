<template>
  <div class="loginNewWrap standard-font" :class="{ other: loginFlag > 0 }">
    <CreateOrPassword v-show="loginFlag == 1" />
    <CreateOrPassword :isForget="true" v-show="loginFlag == 2" />
    <div class="loginPage" v-show="loginFlag == 0">
      <div class="pageItem">
        <div class="leftBox mtop">
          <img class="logo" src="../../../public/static/logo2.png" alt="Logo" />
          <img :class="leftInfos[1].id" :src="leftInfos[1].img" />
          <div class="left_name">{{ leftInfos[1].name }}</div>
          <div class="left_desc">{{ leftInfos[1].desc }}</div>
        </div>
      </div>
      <div v-if="loginActionType === 0" class="pageItem relative">
        <div class="loginBox mtop">
          <div data-pld="login-slogan-text" class="slogan">{{ $t('Login_header_title') }}</div>

          <div class="ssoContainer">
            <div
              ref="googleCoverBtn"
              class="ssoBtn googleBtnContainer"
              :class="{ ssoBtnLoading: isGoogleLoginLoading }"
            >
              <div :class="{ mask: isGoogleLoginLoading }"></div>
              <img class="logo" src="../../../public/static/google.png" />
              <i v-show="isGoogleLoginLoading" class="el-icon-loading mr6"></i>
              <div class="text standard-font">{{ $t('Login_ssologin_google') }}</div>
              <div
                ref="googleOriginBtn"
                class="googleOriginBtn hand"
                @click="handleGoogleBtnClick"
              ></div>
            </div>
            <div class="ssoBtn appleBtnContainer" :class="{ ssoBtnLoading: isAppleLoginLoading }">
              <div :class="{ mask: isAppleLoginLoading }"></div>
              <img class="logo" src="../../../public/static/apple.png" />
              <i v-show="isAppleLoginLoading" class="el-icon-loading mr6"></i>
              <div class="text standard-font">{{ $t('Login_ssologin_apple') }}</div>
              <div
                id="appleid-signin"
                data-type="sign in"
                ref="appleOriginBtn"
                class="appleOriginBtn hand"
                @click="handleAppleBtnClick"
              ></div>
            </div>
            <!-- <div
              class="ssoBtn microsoftBtnContainer"
              :class="{ ssoBtnLoading: isMicrosoftLoginLoading }"
            >
              <div :class="{ mask: isMicrosoftLoginLoading }"></div>
              <img class="logo" src="../../../public/static/microsoft.png" />
              <i v-show="isMicrosoftLoginLoading" class="el-icon-loading mr6"></i>
              <div class="text standard-font">{{ $t('Login_ssologin_microsoft') }}</div>
              <div
                id="microsoftid-signin"
                data-type="sign in"
                ref="microsoftOriginBtn"
                class="microsoftOriginBtn hand"
                @click="handleMicrosoftBtnClick"
              ></div>
            </div> -->
          </div>

          <div class="loginSeparator">
            <div class="separatorLine"></div>
            <div class="separatorText">{{ $t('Login_dict_or') }}</div>
            <div class="separatorLine"></div>
          </div>

          <div class="mb8">
            <MyIpt
              dataPld="login-email-input"
              v-model="login.email"
              :clearError="true"
              :placeholder="$t('Login_email')"
              @doenter="doLogin()"
              @InputBlur="handleEmailBlur"
              from="login"
            />
          </div>
          <div class="mb8">
            <MyIpt
              dataPld="login-password-input"
              v-model="login.password"
              type="password"
              :placeholder="$t('Login_password')"
              :clearError="true"
              @doenter="doLogin()"
            />
          </div>

          <div data-pld="login-forgetpassword-text" class="descForget" @click="updateLoginFlag(2)">
            {{ $t('Login_forget_title') }}
          </div>
          <div class="loginFormErrorWrap">
            <div
              v-if="formError.email"
              class="loginFormError"
              :class="{ visible: formError.email.status }"
            >
              {{ formError.email.msg }}
            </div>
            <div
              v-if="formError.password"
              class="loginFormError"
              :class="{ visible: formError.password.status }"
            >
              {{ formError.password.msg }}
            </div>
          </div>
          <div
            data-pld="login-login-btn"
            class="commonBtn big mt30"
            :class="{ commonBtnLoading: isLoginLoading }"
            @click="doLogin()"
          >
            <div class="text">
              {{ $t('Login_button') }}
              <i v-show="isLoginLoading" class="el-icon-loading mr6 lh20"></i>
            </div>
          </div>
          <div class="createDesc">
            <span>{{ $t('Web_login_registertip') }}</span>
            <div
              data-pld="login-register-text"
              class="btn blueHighlight"
              @click="updateLoginFlag(1)"
            >
              {{ $t('Login_register') }}
            </div>
          </div>
        </div>
        <div class="loginFooter system-font" v-if="loginFlag != 2">
          <!-- By continuing, you agree to PLAUD’s
          <a href="https://note.plaud.ai/user-service-agreement" target="_blank"
            >Terms of Service</a
          >
          and
          <a href="https://note.plaud.ai/privacy" target="_blank">Privacy Policy</a> -->
          <span v-html="translatedPolicyText"></span>
        </div>
        <Loading :status="isLoading" />
      </div>
      <div v-else-if="loginActionType === 1" class="pageItem relative">
        <div class="loginBox mtop">
          <BindAccount data-pld="login-policy-text" @forgetPassword="handleForgetPasswordClick" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from 'vuex';
import Cookies from 'js-cookie';
import CreateOrPassword from '../common/CreateOrPassword';
import MyIpt from '../common/MyIpt.vue';
import BindAccount from '../common/BindAccount.vue';
import { redirectToRedirectParam, urlHasRedirect } from '@/util/frill';

export default {
  name: 'Login',
  components: { MyIpt, BindAccount, CreateOrPassword },
  data() {
    return {
      login: {
        email: '',
        password: '',
      },
      isremb: true,
      formError: {},
      loginFlag: 0,
      leftInfos: [
        {
          id: 'trans',
          img: '/static/transcription.png',
          name: 'Transcribe with Confidence',
          desc: 'Never Miss a Vital Piece',
        },
        {
          id: 'summary',
          img: '/static/summary.png',
          name: 'Access Summaries',
          desc: 'Developed on Best LLMs',
        },
      ],
      loginActionType: 0, // 0: 正常登录，1: sso登录绑定面板
      isLoginLoading: false, // 登录按钮加载状态
      isAppleLoginLoading: false, // apple登录按钮加载状态
      isGoogleLoginLoading: false, // google登录按钮加载状态
      isMicrosoftLoginLoading: false, // microsoft登录按钮加载状态
    };
  },
  computed: {
    ...mapGetters('sso', ['microsoftModel']),
    translatedPolicyText() {
      return this.getTranslateStrsNodes(this.$t('login_policy_text'), [
        { href: '/user-agreement.html', className: 'policy-text' },
        { href: '/privacy.html', className: 'policy-text' },
      ]);
    },
  },
  mounted() {
    this.getCookies();
    this.initSSO();
  },
  methods: {
    ...mapActions('sso', ['initAppleSSO', 'initGoogleSSO', 'initMicrosoftSSO']),
    ...mapMutations('sso', ['SET_SSOTOKEN']),
    initSSO() {
      if (this.isLogin()) return;
      this.initGoogleLogin();
      this.initAppleLogin();
      /* this.initMicrosoftLogin(); */
    },
    initMicrosoftLogin() {
      this.initMicrosoftSSO();
    },
    async handleMicrosoftCallback() {
      this.isMicrosoftLoginLoading = true;

      const loginRequest = {
        scopes: ['user.read'],
      };

      try {
        const response = await this.microsoftModel.loginPopup(loginRequest);
        const payload = {
          id_token: response.idToken,
          sso_type: 'microsoft',
          sso_from: 'web',
        };

        const data = await this.reqPostInfo('/auth/sso-callback', payload);

        this.handleSSOResponse(data, 'microsoft');
      } catch (error) {
        console.log('microsoft sso', error);
        this.setFormError('password', this.$t('Network_faile'));
      } finally {
        this.isMicrosoftLoginLoading = false;
      }
    },
    handleMicrosoftBtnClick() {
      console.log('handleMicrosoftBtnClick', window.msal);
      this.handleMicrosoftCallback();
    },
    initAppleLogin() {
      this.initAppleSSO();
      this.initAppleSSOListener();
    },
    initAppleSSOListener() {
      try {
        document.addEventListener('AppleIDSignInOnSuccess', this.handleAppleSuccessCallback);
        document.addEventListener('AppleIDSignInOnFailure', this.handleAppleFailureCallback);
      } catch (error) {
        console.log('apple sso', error);
        this.isAppleLoginLoading = false;
        this.setFormError('password', this.$t('Network_faile'));
      }
    },
    handleAppleBtnClick() {
      console.log('handleAppleBtnClick', window.apple);
    },
    async handleAppleSuccessCallback(event) {
      this.isAppleLoginLoading = true;

      const { id_token } = event.detail.authorization;
      const payload = {
        id_token,
        sso_type: 'apple',
        sso_from: 'web',
      };

      try {
        const data = await this.reqPostInfo('/auth/sso-callback', payload);
        this.handleSSOResponse(data, 'apple');
      } catch (error) {
        console.log('handleAppleSuccessCallback', error);
        this.setFormError('password', this.$t('Network_faile'));
      } finally {
        this.isAppleLoginLoading = false;
      }
    },
    async handleAppleFailureCallback(event) {
      console.log('handleAppleFailureCallback', event);
      this.isAppleLoginLoading = false;
      this.setFormError('password', this.$t('Network_faile'));
    },
    initGoogleLogin() {
      const { handleGoogleCallback } = this;
      const googleOriginBtnElement = this.$refs.googleOriginBtn;
      const googleCoverBtnElement = this.$refs.googleCoverBtn;
      this.initGoogleSSO({
        googleOriginBtnElement,
        googleCoverBtnElement,
        googleLoginCallback: handleGoogleCallback,
      });
    },
    async handleGoogleCallback(response) {
      this.isGoogleLoginLoading = true;

      const payload = {
        sso_from: 'web',
        sso_type: 'google',
        id_token: response.credential,
      };

      try {
        const data = await this.reqPostInfo('/auth/sso-callback', payload);
        this.handleSSOResponse(data, 'google');
      } catch (error) {
        console.log('reqPostInfo fail', error);
        this.setFormError('password', this.$t('Network_faile'));
      } finally {
        this.isGoogleLoginLoading = false;
      }
    },
    handleGoogleBtnClick() {
      console.log('handleGoogleBtnClick', window.google);
    },
    async goToFrill() {
      let data = await this.reqPostInfo('/auth/access-token-other', { client_id: 'frill' });
      let token = data?.access_token || '';
      this.SET_SSOTOKEN(token);
      redirectToRedirectParam(token, true);
    },
    async doLogin() {
      this.setGALogEvent('web_loginwith_email');

      if (!this.login.email) {
        this.formError = {
          email: {
            status: true,
            msg: this.$t('Login_edit_noemail'),
          },
        };
        return false;
      }

      if (!this.login.password) {
        this.formError = {
          password: {
            status: true,
            msg: this.$t('Login_edit_nopassword'),
          },
        };
        return false;
      }
      this.isLoginLoading = true;
      this.formError = {};

      const formData = new FormData();
      formData.append('username', this.login.email);
      formData.append('password', this.login.password);
      formData.append('client_id', 'web');

      try {
        const data = await this.reqPostInfo('/auth/access-token', formData);
        this.setGALogEvent('web_loginwith_email_success', { email: this.login.email });
        this.isLoginLoading = false;

        const { access_token, token_type } = data;
        localStorage.setItem('tokenstr', `${token_type} ${access_token}`);
        this.$store.commit('auth/AUTH_SET_CURRENT_LOGIN_TYPE', 'email'); // 邮箱登录更新登录类型
        this.$store.commit('auth/AUTH_SET_CURRENT_LOGIN_ACCOUNT', this.login.email); // 邮箱登录更新登录账号
        this.goToFrill();
        !urlHasRedirect() && this.$parent.loginSuccess();
      } catch (error) {
        console.log('doLogin', error);
        this.isLoginLoading = false;
        if (error.status === -3) {
          this.setFormError('password', this.$t('Network_sso_login_err_3'));
        } else {
          this.setFormError('password', this.$t('Login_passwordwrong'));
        }
      }
    },
    updateLoginFlag(flag) {
      if (flag == 2) {
        this.setGALogEvent('web_forget_email');
      }
      this.loginFlag = flag;
      this.formError = {};
    },
    setFormError(field, message) {
      this.formError = {
        [field]: {
          status: true,
          msg: message,
        },
      };
    },
    handleSSOResponse(data, ssoType) {
      const { status, access_token, token_type } = data;

      switch (status) {
        case 0:
          localStorage.setItem('tokenstr', `${token_type} ${access_token}`);
          this.$store.commit('auth/AUTH_SET_CURRENT_LOGIN_TYPE', ssoType);
          this.goToFrill();
          !urlHasRedirect() && this.$parent.loginSuccess();
          // test
          break;
        case 1:
          this.$store.commit('auth/AUTH_SET_CURRENT_LOGIN_TYPE', ssoType);
          this.$store.commit('auth/AUTH_SET_AUTH_INFO', data);
          this.loginActionType = 1;
          break;
        case -10:
          this.setFormError('password', this.$t('Network_faile'));
          break;
        default:
          this.setFormError('password', this.$t('Network_faile'));
          break;
      }
    },
    handleForgetPasswordClick() {
      this.updateLoginFlag(2);
    },
    handleEmailBlur() {
      // 失焦更新符合邮箱格式的输入邮箱
      if (!this.isNotEmail(this.login.email)) {
        this.$store.commit('cache/CACHE_SET_LOGIN_EMAIL', this.login.email);
      }
    },
    getCookies() {
      const cookEmail = Cookies.get('noteemail');
      const cookPwd = Cookies.get('notepwd');
      if (cookEmail) {
        this.$set(this.login, 'email', cookEmail);
      }
      if (cookPwd) {
        this.$set(this.login, 'password', window.atob(cookPwd));
      }
    },
    isLogin() {
      let tokenstr = localStorage.getItem('tokenstr');
      return !!tokenstr;
    },
  },
};
</script>
