<template>
  <div class="apidoc-wrapper">
    <div class="header">
      <div class="header-logo" @click="goHome">
        <img class="logo" :src="getImagePath('plaud_logo_text')" alt="logo" />
      </div>
    </div>
    <div class="main">
      <div class="colorful left"></div>
      <div class="content">
        <h1 class="title">Zapier <> Plaud Integration</h1>

        <h2 class="subtitle">Sample APIs Requests</h2>
        <p>
          <strong>Note: </strong>These are the current public APIs available. Our team will continue
          to work on adding more APIs in the future.
        </p>

        <div v-for="(item, index) in apiData" :key="index">
          <h3 class="api-path">{{ item.title }}</h3>
          <p><strong>Description: </strong>{{ item.description }}</p>
          <p style="margin: 8px 0"><strong>Headers: </strong></p>
          <ul v-for="(subitem, subindex) in item.headers" :key="subindex">
            <li class="circle">{{ subitem }}</li>
          </ul>
          <p style="margin: 8px 0 0 0" v-if="item.requestBody.length > 0">
            <strong>Request Body:</strong>
          </p>
          <pre class="code" v-html="item.code1" v-if="item.code1"></pre>
          <ul v-if="item.requestBody.length > 0">
            <li class="circle" v-for="(ritem, subindex) in item.requestBody" :key="subindex">
              {{ ritem }}
            </li>
          </ul>
          <p style="margin: 8px 0 0 0" v-if="item.code2 || item.response.length > 0">
            <strong>Response: </strong>
          </p>
          <pre class="code" v-html="item.code2" v-if="item.code2"></pre>
          <ul v-if="item.response.length > 0">
            <li class="circle" v-for="(ritem, subindex) in item.response" :key="subindex">
              {{ ritem }}
            </li>
          </ul>
          <p style="margin: 8px 0px"><strong>Common Errors: </strong></p>
          <ul>
            <li class="circle" v-for="(subitem, subindex) in item.errors" :key="subindex">
              <strong>{{ subitem.strong }}</strong> {{ subitem.text }}
            </li>
          </ul>
        </div>
      </div>
      <div class="colorful right"></div>
    </div>
  </div>
</template>
<script>
// import { mapState, mapGetters, mapMutations } from 'vuex';
import { getBaseUrl } from '@/util/common';
import { getAuthURLs } from '@/apis/auth';

export default {
  components: {},
  props: {
    pageType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      apiData: [
        {
          title: 'POST /auth/apps/zapier/token',
          description:
            'This endpoint is used to exchange an authorization code for an access token in an OAuth 2.0 authentication flow. The client must provide a valid authorization code, along with the client ID and client secret, to obtain an access token.',
          headers: ['content-type: application/json'],
          code1:
            '{ <br>  "<strong>code</strong>": "code",<br>  "<strong>client_id</strong>": "client_id",<br>  "<strong>client_secret</strong>": "client_secret",<br>  "<strong>grant_type</strong>": "authorization_code",<br>}',
          requestBody: [
            'code (string, required) – The authorization code received from the authorization server after the user grants permission.',
            'client_id (string, required) – The unique identifier assigned to the client application.',
            'client_secret (string, required) – The secret key associated with the client application, used for authentication.',
            'grant_type (string, required) – The type of authorization grant being used. For this request, it must be "authorization_code".',
          ],
          code2:
            '{ <br>  "<strong>access_token</strong>": "access_token",<br>  "<strong>expires_in</strong>": "36000",<br>  "<strong>token_type</strong>": "Bearer",<br>  "<strong>refresh_token</strong>": "refresh_token"<br>}',
          response: [
            'access_token (string) – The access token issued by the authorization server, used for authenticated API requests.',
            'expires_in (integer) – The duration (in seconds) for which the access token remains valid.',
            'token_type (string) – The type of token issued. Typically, this is "Bearer".',
            'refresh_token (string) – A token that can be used to obtain a new access token without requiring user authentication again.',
          ],
          errors: [{ strong: 'HTTP 400 Bad Request.', text: 'Invalid client credentials' }],
        },
        {
          title: 'POST /auth/apps/zapier/refresh-token',
          description:
            'This endpoint is used to obtain a new access token using a refresh token in an OAuth 2.0 authentication flow. When the current access token expires, the client can use the refresh token to request a new one without requiring user reauthorization.',
          headers: ['content-type: application/json', 'Authorization: Bearer $access_token'],
          code1:
            '{ <br>  "<strong>grant_type</strong>": "refresh_token",<br>  "<strong>refresh_token</strong>": ""<br>}',
          requestBody: [
            'grant_type (string, required) – The type of authorization grant being used. For this request, it must be "refresh_token".',
            'refresh_token (string, required) – The refresh token obtained from a previous authentication response, used to request a new access token.',
          ],
          code2:
            '{ <br>  "<strong>access_token</strong>": "new_access_token",<br>  "<strong>expires_in</strong>": "36000",<br>  "<strong>token_type</strong>": "Bearer",<br>  "<strong>refresh_token</strong>": "new_refresh_token"<br>}',
          response: [
            'access_token (string) – The access token issued by the authorization server, used for authenticated API requests.',
            'expires_in (integer) – The duration (in seconds) for which the access token remains valid.',
            'token_type (string) – The type of token issued. Typically, this is "Bearer".',
            'refresh_token (string) – The newly issued refresh token, which can be used to obtain another access token when the current one expires.',
          ],
          errors: [{ strong: 'HTTP 400 Bad Request.', text: 'Invalid refresh token' }],
        },
        {
          title: 'GET /user/public/me',
          description:
            'This API returns the name and email of the user owning the access_token. It is used to authenticate the access_token with Plaud.',
          headers: ['content-type: application/json', 'Authorization: Bearer $access_token'],
          code1:
            '{ <br>  "<strong>id</strong>": "id",<br>  <strong>email</strong>": "<EMAIL>",<br>  <strong>nickname</strong>": "usename"<br>}',
          requestBody: [
            'id (string) – The unique identifier assigned to the user.',
            "email (string) – The user's email address, used for communication and authentication.",
            "nickname (string) – The user's chosen display name or username.",
          ],
          code2: '',
          response: [],
          errors: [
            {
              strong: 'HTTP 400 Bad Request.',
              text: '"Authorization" is missing from the request header',
            },
            { strong: 'HTTP 401 Unauthorized.', text: 'Invalid access_token' },
          ],
        },
        {
          title: 'POST /integrations/zapier/triggers',
          description:
            'This API allows you to subscribe to a specific event within the integration. When the event occurs, a webhook notification will be sent to the specified hook_url.',
          headers: ['content-type: application/json', 'Authorization: Bearer $access_token'],
          code1:
            '{ <br>  "<strong>event</strong>": "new_ai_generation_complete",<br>  "<strong>hook_url</strong>": "https://hooks.zapier.com/abc123"<br>}',
          requestBody: [
            'event (string): The event type to subscribe to.',
            'hook_url (string): The webhook URL where event notifications will be sent.',
          ],
          code2: '{ <br>  "<strong>id</strong>": "1"<br>}',
          response: ['id (integer): The unique trigger ID.'],
          errors: [
            {
              strong: 'HTTP 400 Bad Request.',
              text: '"Authorization" is missing from the request header',
            },
            { strong: 'HTTP 401 Unauthorized.', text: 'Invalid access_token' },
          ],
        },
        {
          title: 'DELETE /integrations/zapier/triggers/{trigger_id}',
          description:
            'This API allows you to cancel a previously created event subscription using the trigger ID.',
          headers: ['content-type: application/json', 'Authorization: Bearer $access_token'],
          code1: '',
          requestBody: [],
          code2: '',
          response: ['No response body if successful.'],
          errors: [
            {
              strong: 'HTTP 400 Bad Request.',
              text: '"Authorization" is missing from the request header',
            },
            { strong: 'HTTP 401 Unauthorized.', text: 'Invalid access_token' },
          ],
        },
        {
          title: 'GET /integrations/zapier/triggers/new_ai_generation_complete/perform-list',
          description:
            'This API retrieves a list of execution records for new_ai_generation_complete. Each record contains details such as the file title, transcript, summary, and creation time.',
          headers: ['content-type: application/json', 'Authorization: Bearer $access_token'],
          code1:
            '{ <br>  "<strong>title</strong>": "Test file name",<br>  "<strong>transcript</strong>": "00:01:11 Speaker 1 Welcome to.....Thank you",<br>  "<strong>summary</strong>": "here is the summary, example: Meeting Record......",<br>  "<strong>create_time</strong>": "2025-01-01T12:00:00"<br>}',
          requestBody: [
            'title (string): The title of the generated file.',
            'transcript (string): The full transcript of the event.',
            'summary (string): A summarized version of the event content.',
            'create_time (string, format: YYYY-MM-DDTHH:mm:ss): The timestamp when the record was created.',
          ],
          code2: '',
          response: [],
          errors: [
            {
              strong: 'HTTP 400 Bad Request.',
              text: '"Authorization" is missing from the request header',
            },
            { strong: 'HTTP 401 Unauthorized.', text: 'Invalid access_token' },
          ],
        },
      ],
    };
  },
  computed: {},
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    init() {},
    goHome() {
      window.location.href = getBaseUrl(window.location.href) || 'https://app.plaud.ai/';
    },
    getImagePath(icon) {
      try {
        return require(`../../../public/static/${icon}.png`);
      } catch (e) {
        console.error(`Image not found: ../../../public/static/${icon}.png`, e);
        return '';
      }
    },
  },
};
</script>
<style scoped lang="scss">
.apidoc-wrapper {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: #1f1f1f;
}
.auth-wrapper {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: #1f1f1f;
}
.header {
  width: 100%;
  height: 48px;
  z-index: 2;
  background: #1f1f1f;
  .header-logo {
    height: 100%;
    width: 180px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .logo {
    margin-left: 24px;
    width: 153px;
    height: 20px;
  }
}
.main {
  position: relative;
  z-index: 1;
  flex: 1;
  line-height: 1.6;
  overflow-y: auto;
  overflow-x: hidden;
  .content {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 6%;
    min-width: 600px;
    padding: 80px 48px 40px;
    border-radius: 20px;
    background: #fff;
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
    .title {
      font-weight: 500;
      text-align: center;
    }
    h1 {
      text-align: center;
      font-size: 34px;
    }
    h2 {
      font-size: 26px;
    }
    h3 {
      font-size: 22px;
    }
    p {
      font-size: 16px;
    }
    pre {
      background: #f7f7f7;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 10px 15px;
      overflow: auto;
      white-space: pre;
      margin: 16px 0;
    }
    .subtitle {
      margin-bottom: 10px;
    }
    .api-path {
      margin: 22px 0 8px;
    }
    .circle {
      padding-left: 10px;
      position: relative;
      font-size: 16px;
      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 4px;
        margin-bottom: 5px;
        background: black;
        border-radius: 100%;
        position: absolute;
        left: 0;
        top: 11px;
        // transform: translateY(-50%);
      }
    }
    .code {
      font-size: 14px;
    }
  }

  .colorful {
    z-index: -1;
    position: absolute;
    width: 900px;
    height: 471px;
    border-radius: 900px;
  }
  .left {
    left: -8%;
    bottom: -10%;
    opacity: 0.3;
    background: linear-gradient(90deg, #949fff 0%, #ff7cbb 100%);
    filter: blur(100px);
  }
  .right {
    right: -8%;
    top: -10%;
    background: linear-gradient(80deg, #eee4ff 11.14%, #e1f4ff 101.08%);
    filter: blur(75px);
  }
}
</style>
