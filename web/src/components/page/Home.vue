<template>
  <div class="homeBody">
    <vue-perfect-scrollbar class="leftBox" ref="leftBar" :class="{ smallBox: leftSmallStatus }">
      <div class="leftBox-inner">
        <template v-if="leftSmallStatus">
          <div class="topBtn">
            <el-tooltip effect="dark" :content="$t('expand_sidebar')" placement="bottom">
              <div
                class="iconfont lrBtn icon-sidebar_right"
                @click="changeSmallStatus(1, false)"
              ></div>
            </el-tooltip>
          </div>
          <div class="secondBox">
            <div class="secondBtnBox">
              <div v-if="isShowDesktopDownload" class="uploadBtn" @click="onRecord()">
                <div data-pld="home-sidebar-desktop-download-btn" class="icon-wrapper">
                  <img :src="getImagePath('icon_start_recording')" alt="icon" />
                </div>
              </div>
              <div class="uploadBtn" @click="doUpladFiles()">
                <div data-pld="home-sidebar-import-btn" class="icon-wrapper">
                  <img :src="getImagePath('icon_import_audio')" alt="icon" />
                </div>
              </div>
              <!-- global askai -->
              <div
                class="myBtn askaiBtn askai-small"
                :class="{ active: info.key == isCenterStatus }"
                v-for="info in featureList"
                :key="info.id"
                @click="doFeature(info)"
              >
                <div :data-pld="`home-sidebar-${info.name}-btn`" class="icon-wrapper">
                  <img :src="getImagePath(info.icon)" alt="icon" />
                </div>
                <!-- <div class="member-ship">
                <img :src="getImagePath(membershipIconName)" alt="icon" />
              </div> -->
              </div>
              <!-- 模版社区 -->
              <div class="uploadBtn" @click="goTemplateCommunity">
                <div data-pld="home-sidebar-template_community-btn" class="icon-wrapper">
                  <img :src="getImagePath('benefit')" alt="icon" />
                </div>
              </div>
              <el-popover
                popper-class="elPopoverClass"
                placement="right-start"
                width="160"
                :visible-arrow="false"
                trigger="hover"
              >
                <vue-perfect-scrollbar class="tagMenuList popTagMenuList">
                  <li
                    :class="{ active: !selectMenu && isCenterStatus == -1 }"
                    @click="selectLeftMenu('', 0)"
                  >
                    <div class="iconfont icon-folder-foler" style="color: #101828"></div>
                    <div class="name">
                      {{ $t('Filelist_allfiles')
                      }}<span class="count">({{ getWholeCount(0) }})</span>
                    </div>
                  </li>
                  <li class="line"></li>
                  <li
                    v-for="tag in tagList"
                    :key="tag.id"
                    :class="{ active: selectMenu == tag.id && isCenterStatus == -1 }"
                    @click="selectLeftMenu(tag.id, 1)"
                  >
                    <div
                      class="left iconfont"
                      v-html="getIconCode(tag.icon)"
                      :style="{ color: tag.color }"
                    ></div>
                    <div class="name">
                      {{ getNameEllipse(tag.name, 20)
                      }}<span class="count">({{ getTagCount(tag.id) }})</span>
                    </div>
                  </li>
                </vue-perfect-scrollbar>
                <template v-slot:reference>
                  <div class="myBtn">
                    <div
                      class="iconfont"
                      v-for="info in folderTitleList.slice(0, 1)"
                      :key="info.id"
                      :class="['icon-' + info.icon]"
                      :style="{ color: info.color }"
                    ></div>
                  </div>
                </template>
              </el-popover>
              <div
                class="myBtn"
                :class="{ active: selectMenu == info.id && isCenterStatus == -1 }"
                @click="selectLeftMenu(info.id, info.logid)"
                v-for="info in folderTitleList.slice(1, 3)"
                :key="info.id"
              >
                <div
                  class="iconfont"
                  :class="['icon-' + info.icon]"
                  :style="{ color: info.color }"
                ></div>
              </div>
              <el-popover
                popper-class="elPopoverClass"
                placement="right-start"
                width="160"
                :visible-arrow="false"
                trigger="hover"
              >
                <vue-perfect-scrollbar class="tagMenuList popTagMenuList">
                  <li
                    v-for="tag in defaultList"
                    :key="tag.id"
                    :class="{ active: selectMenu == tag.id && isCenterStatus == -1 }"
                    @click="selectLeftMenu(tag.id, 2)"
                  >
                    <div class="name">{{ getNameEllipse(tag.name, 20) }}</div>
                  </li>
                </vue-perfect-scrollbar>
                <template v-slot:reference>
                  <div class="myBtn">
                    <div
                      class="iconfont icon-drive_folder_upload"
                      :style="{ color: '#98A2B3' }"
                    ></div>
                  </div>
                </template>
              </el-popover>
            </div>
            <div class="secondUserBox">
              <div class="uNameIcon hand" @click="toCenter(0)">
                <img :src="userInfo.avatar" v-if="userInfo.avatar" />
                <span v-else>{{ userInfo.nickname ? userInfo.nickname.substring(0, 1) : '' }}</span>
              </div>
              <div class="myBtn" @click="toCenter(2)">
                <div class="iconfont icon-sidebar-support"></div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="logoTop">
            <img src="../../../public/static/logo.png" alt="" />
            <el-tooltip effect="dark" :content="$t('collapse_sidebar')" placement="bottom">
              <div
                class="iconfont lrBtn icon-sidebar_left"
                @click="changeSmallStatus(1, true)"
              ></div>
              <!-- <img
              class="iconfont lrBtn icon-sidebar_left"
              :src="getImagePath('icon_side_collaspe')"
              alt="icon"
            /> -->
            </el-tooltip>
          </div>
          <div v-if="isShowDesktopDownload" class="btnCont" id="start_record">
            <div
              class="uploadBtn font-semibold"
              :class="{ active: isCenterStatus === 30 }"
              @click="onRecord()"
              style="margin-bottom: 0px"
            >
              <div data-pld="home-sidebar-desktop-download-btn" class="icon-wrapper">
                <img :src="getImagePath('icon_start_recording')" alt="icon" />
              </div>
              <span class="overflow-hidden whitespace-nowrap text-ellipsis">{{
                $t('start_recording')
              }}</span>
            </div>
          </div>
          <div class="btnCont">
            <div
              class="uploadBtn overflow-hidden whitespace-nowrap text-ellipsis font-semibold"
              :class="{ active: isCenterStatus === 31 }"
              @click="doUpladFiles()"
            >
              <div data-pld="home-sidebar-import-btn" class="icon-wrapper">
                <img :src="getImagePath('icon_import_audio')" alt="icon" />
              </div>
              {{ $t('Import_audio_web_button') }}
            </div>
          </div>

          <!-- global askai -->
          <div class="btnCont">
            <!-- 骨架屏 -->
            <div class="tag-skeleton pb-0" v-if="isWholeLoading">
              <el-skeleton class="tag-skeleton-item" animated>
                <template slot="template">
                  <el-skeleton-item variant="div" style="width: 100%; height: 12px" />
                </template>
              </el-skeleton>
            </div>
            <div
              v-else
              class="uploadBtn askaiBtn"
              :class="{ active: info.key == isCenterStatus }"
              v-for="info in featureList"
              :key="info.id"
              @click="doFeature(info)"
            >
              <div :data-pld="`home-sidebar-${info.name}-btn`" class="flex-middle relative w-full">
                <div class="icon-wrapper">
                  <img :src="getImagePath(info.icon)" alt="icon" />
                </div>
                {{ info.name }}
                <div class="member-ship">
                  <img :src="getImagePath(membershipIconName)" alt="icon" />
                </div>
                <div class="keyboard">
                  <div class="item">{{ ctrlKeyboard }}</div>
                  <div class="item">/</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 模版社区 -->
          <div class="btnCont">
            <!-- 骨架屏 -->
            <div class="tag-skeleton pb-0" v-if="isWholeLoading">
              <el-skeleton class="tag-skeleton-item" animated>
                <template slot="template">
                  <el-skeleton-item variant="div" style="width: 100%; height: 12px" />
                </template>
              </el-skeleton>
            </div>
            <div
              v-else
              class="uploadBtn"
              :class="{ active: statusList.templateCommunityStatus == isCenterStatus }"
              @click="goTemplateCommunity"
            >
              <div
                :data-pld="`home-sidebar-template_community-btn`"
                class="flex-middle relative w-full"
              >
                <div class="icon-wrapper">
                  <img :src="getImagePath('benefit')" alt="icon" />
                </div>
                {{ this.$t('template_community_title') }}
                <div class="member-ship">
                  <img :src="getImagePath('badge_beta')" alt="icon" />
                </div>
              </div>
            </div>
          </div>
          <div class="line"></div>

          <div class="leftCont">
            <div class="leftCont_top">
              <div
                class="foldTitle overflow-hidden whitespace-nowrap text-ellipsis"
                v-for="info in folderTitleList"
                :key="info.id"
                :class="{ active: selectMenu == info.id && isCenterStatus == -1 }"
                @click="selectLeftMenu(info.id, info.logid)"
              >
                <div class="flex-middle">
                  <div
                    class="iconfont"
                    style="margin-right: 8px"
                    :class="['icon-' + info.icon]"
                    :style="{ color: info.color }"
                  ></div>
                  {{ info.name }}
                </div>
                <span>{{ getWholeCount(info.logid) }}</span>
              </div>
              <div class="maxCont ps" style="margin-top: 16px">
                <div class="titleBox">
                  <div class="flex-middle">
                    {{ $t('Siderbar_folders') }}
                    <el-tooltip
                      effect="dark"
                      :content="folderStatus ? $t('collapse') : $t('expand')"
                      placement="right"
                    >
                      <div class="expandBtn">
                        <div
                          class="iconfont w-6 h-6 flex items-center justify-center rounded hover:bg-gray3 cursor-pointer"
                          :class="[folderStatus ? 'mIcon icon-sidebar_clsoure' : 'icon-next']"
                          @click="folderStatus = !folderStatus"
                        ></div>
                      </div>
                    </el-tooltip>
                  </div>
                  <el-tooltip effect="dark" :content="$t('add_folder')" placement="bottom">
                    <div class="addBtn" @click="addTag()">
                      <div
                        class="iconfont icon-sidebar-Folder-add w-6 h-6 flex items-center justify-center rounded hover:bg-gray3 cursor-pointer"
                      ></div>
                    </div>
                  </el-tooltip>
                </div>
                <!-- 骨架屏 -->
                <div class="tag-skeleton" v-if="isWholeLoading">
                  <el-skeleton class="tag-skeleton-item" animated>
                    <template slot="template">
                      <el-skeleton-item
                        variant="div"
                        style="width: 100%; height: 12px; margin-bottom: 4px"
                      />
                      <el-skeleton-item variant="div" style="width: 100%; height: 12px" />
                    </template>
                  </el-skeleton>
                </div>
                <ul
                  class="tagMenuList"
                  :class="{ hasMore: tagList.length > 1, hide: !folderStatus }"
                  v-else
                >
                  <li
                    v-for="tag in tagList"
                    :key="tag.id"
                    :class="{ active: selectMenu == tag.id && isCenterStatus == -1 }"
                    @click="selectLeftMenu(tag.id, 1)"
                    @dragover="handleDragOver($event, tag)"
                    @drop="handleDrop($event, tag)"
                    @dragleave="handleDragLeave($event, tag)"
                    @dragenter="handleDragEnter($event, tag)"
                    :ref="'tag-' + tag.id"
                  >
                    <div
                      class="left iconfont"
                      :style="{ color: tag.color }"
                      v-html="getIconCode(tag.icon)"
                    ></div>
                    <div class="name ellipsis">
                      {{ getNameEllipse(tag.name, 16)
                      }}<span class="count">({{ getTagCount(tag.id) }})</span>
                    </div>
                    <div class="right">
                      <div
                        class="iconfont icon-sidebar-Folder-more"
                        @click.stop="addTag(tag)"
                      ></div>
                    </div>
                  </li>
                </ul>
                <div class="titleBox" :class="{ mtop: folderStatus }">
                  <div class="flex-middle">
                    {{ $t('Siderbar_modes') }}
                    <el-tooltip
                      effect="dark"
                      :content="comesStatus ? $t('collapse') : $t('expand')"
                      placement="right"
                    >
                      <div class="expandBtn">
                        <div
                          class="iconfont w-6 h-6 flex items-center justify-center rounded hover:bg-gray3 cursor-pointer"
                          :class="[comesStatus ? 'mIcon icon-sidebar_clsoure' : 'icon-next']"
                          @click="changeListStatus"
                        ></div>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <ul class="tagMenuList" :class="{ hide: !comesStatus }">
                  <li
                    v-for="tag in defaultList"
                    :key="tag.id"
                    :class="{ active: selectMenu == tag.id && isCenterStatus == -1 }"
                    @click="selectLeftMenu(tag.id, 2)"
                  >
                    <div class="name">{{ getNameEllipse(tag.name, 20) }}</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="leftCont_bottom" style="margin-top: 10px">
              <div
                class="memberInfo"
                :class="{ active: isCenterStatus === 1 }"
                @click="toCenter(1)"
              >
                <div class="uNameIcon leftBig">
                  <img :src="userInfo.avatar" v-if="userInfo.avatar" />
                  <span class="noimg" v-else>{{
                    userInfo.nickname ? userInfo.nickname.substring(0, 1) : ''
                  }}</span>
                </div>
                <!-- 骨架屏 -->
                <div class="centerName tag-skeleton cname-skeleton" v-if="!userInfo.nickname">
                  <el-skeleton class="tag-skeleton-item" animated>
                    <template slot="template">
                      <el-skeleton-item
                        variant="div"
                        style="width: 70%; margin-bottom: -2px; height: 12px"
                      />
                      <el-skeleton-item variant="div" style="width: 35%; height: 12px" />
                    </template>
                  </el-skeleton>
                </div>
                <div class="centerName" v-else>
                  <div class="name">{{ userInfo.nickname }}</div>
                  <div
                    class="levelInfo"
                    :class="[userStateInfo.membership_type]"
                    v-if="userInfo.membership_id"
                  >
                    {{
                      ['pro', 'backer', 'unlimited'].indexOf(userStateInfo.membership_type) > -1
                        ? userStateInfo.membership_type
                        : $t('Me_membercard_upgrade')
                    }}
                  </div>
                </div>
                <div class="circle-red" style="margin-right: 3px" v-if="redDotShow"></div>
                <div class="iconfont icon-next"></div>
              </div>
              <div class="tag-skeleton time-skeleton" v-if="!userInfo.id">
                <el-skeleton class="tag-skeleton-item" animated>
                  <template slot="template">
                    <el-skeleton-item variant="div" style="width: 100%; height: 64px" />
                  </template>
                </el-skeleton>
              </div>
              <div
                :class="{ leftTimeWrapper: !isUnlimitedUser }"
                @click="toCenter(0)"
                data-pld="member-entry-btn"
                v-else
              >
                <div class="leftTimeBox" :class="[userStateInfo.membership_type]">
                  <div class="memberUserInfo" v-if="userInfo.membership_id">
                    <div class="name linerTxt" style="display: inline-block">{{ memberName }}</div>
                    <template>
                      <div class="barBox">
                        <div
                          v-if="!isUnlimitedUser"
                          class="barpro"
                          :style="{
                            width: getPerNum(userInfo.seconds_left, userInfo.seconds_total) + '%',
                          }"
                        ></div>
                        <AmLoad type="unlimited_shining" v-else />
                      </div>
                      <div
                        class="time linerTxt"
                        style="display: inline-block"
                        v-if="userStateInfo.is_free_trial_now"
                        v-html="
                          getTranslateStr(
                            'web_free_trial_card_conut_days_left',
                            '%s',
                            getFreeLeftDay(),
                          )
                        "
                      ></div>
                      <div class="time" v-else-if="isUnlimitedUser">
                        {{
                          getTranslateStr(
                            'Web_me_membercard_exspire',
                            '%s',
                            getDateStr(userInfo.expire_time * 1000, 'yyyy-MM-dd'),
                          )
                        }}
                      </div>
                      <div class="time" v-else>
                        {{
                          getTranslateStr(
                            'Me_membercard_subtitle_lefttime_left',
                            '20240327',
                            getMinNum(userInfo.seconds_left),
                          )
                        }}
                      </div>
                    </template>
                  </div>
                  <div class="noneUser" v-else>
                    <div class="starIcon"></div>
                    <div class="title mb4">{{ $t('member_starter_unlock_card_title') }}</div>
                    <div class="desc">{{ $t('member_starter_unlock_card_text') }}</div>
                    <div class="unlockBtn hand" @click.stop="doUpladFiles()">
                      {{ $t('member_starter_unlock_card_btn') }}
                    </div>
                  </div>
                  <div class="trafficBox" v-if="userInfo.membership_id_traffic">
                    <div class="name">{{ packageInfo.traffic }}</div>
                    <div class="barBox">
                      <div
                        class="barpro"
                        :style="{
                          width:
                            getPerNum(
                              userInfo.seconds_left_traffic,
                              userInfo.seconds_total_traffic,
                            ) + '%',
                        }"
                      ></div>
                    </div>
                    <div class="time">
                      {{
                        getTranslateStr(
                          'Me_membercard_quota_lefttime_left',
                          '20240327',
                          getMinNum(userInfo.seconds_left_traffic),
                        )
                      }}
                    </div>
                  </div>
                  <div class="moreBtn" v-if="isShowNeedMoreBtn" @click.stop="toCenter(4)">
                    {{ $t('member_dict_need_more') }}
                  </div>
                  <!-- 没有订阅过 没有试用过 -->
                  <div class="freeBox" v-if="hasFreeShow && userInfo.membership_id">
                    <div class="giftImg"></div>
                    <div class="giftTxt">{{ $t('Membership_plandedtails_freetrail') }}</div>
                  </div>
                </div>
              </div>
              <div
                class="supportInfo foldTitle flex gap-2"
                :class="{ active: isCenterStatus === 5 }"
                style="justify-content: flex-start; margin-bottom: 0 !important"
                @click="toCenter(1)"
              >
                <svg
                  width="19"
                  height="20"
                  viewBox="0 0 19 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_8391_11949)">
                    <circle
                      cx="14.9453"
                      cy="6.97974"
                      r="3.17896"
                      fill="url(#paint0_linear_8391_11949)"
                    />
                    <circle
                      cx="5.10031"
                      cy="14.1741"
                      r="3.17896"
                      fill="url(#paint1_linear_8391_11949)"
                    />
                    <path
                      d="M5.06052 10.7686C6.66382 10.7689 8.00199 11.9061 8.31149 13.418H17.6582L17.7793 13.4297C18.0525 13.4857 18.2576 13.7278 18.2578 14.0176C18.2578 14.3075 18.0526 14.5494 17.7793 14.6055L17.6582 14.6172H8.33591C8.08201 16.1976 6.71245 17.405 5.06052 17.4053C3.22784 17.4053 1.74229 15.9196 1.74216 14.0869C1.74224 12.2542 3.22781 10.7686 5.06052 10.7686ZM5.06052 11.9678C3.89055 11.9678 2.94146 12.917 2.94138 14.0869C2.94151 15.2568 3.89058 16.2051 5.06052 16.2051C6.23016 16.2047 7.17855 15.2566 7.17868 14.0869C7.1786 12.9172 6.23019 11.9681 5.06052 11.9678ZM14.9394 3.59473C16.7722 3.59473 18.2578 5.08033 18.2578 6.91309C18.2574 8.74555 16.772 10.2314 14.9394 10.2314C13.2877 10.2311 11.9192 9.02346 11.665 7.44336H2.34177L2.22067 7.43164C1.94744 7.37564 1.74228 7.13351 1.74216 6.84375C1.74216 6.55387 1.94736 6.31188 2.22067 6.25586L2.34177 6.24414H11.6884C11.9977 4.73203 13.3359 3.59504 14.9394 3.59473ZM14.9394 4.79395C13.7697 4.79431 12.8213 5.74329 12.8213 6.91309C12.8216 8.08259 13.7699 9.03089 14.9394 9.03125C16.1092 9.03125 17.0582 8.08281 17.0586 6.91309C17.0586 5.74307 16.1094 4.79395 14.9394 4.79395Z"
                      fill="#1F1F1F"
                    />
                  </g>
                  <defs>
                    <linearGradient
                      id="paint0_linear_8391_11949"
                      x1="14.9453"
                      y1="4.94561"
                      x2="14.9453"
                      y2="6.78305"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#E4E7EC" />
                      <stop offset="1" stop-color="#E4E7EC" stop-opacity="0" />
                    </linearGradient>
                    <linearGradient
                      id="paint1_linear_8391_11949"
                      x1="5.10031"
                      y1="12.1399"
                      x2="5.10031"
                      y2="13.9774"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#E4E7EC" />
                      <stop offset="1" stop-color="#E4E7EC" stop-opacity="0" />
                    </linearGradient>
                    <clipPath id="clip0_8391_11949">
                      <rect width="19" height="19" fill="white" transform="translate(0 0.5)" />
                    </clipPath>
                  </defs>
                </svg>

                {{ $t('Web_setting') }}
              </div>
              <div
                v-if="isShowDesktopDownload"
                class="supportInfo foldTitle flex gap-2"
                :class="{ active: isCenterStatus === 5 }"
                style="justify-content: flex-start; margin-bottom: 0 !important"
                @click="toCenter(101)"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 17.9688C8.90104 17.9688 7.86979 17.7604 6.90625 17.3438C5.94271 16.9323 5.09635 16.362 4.36719 15.6328C3.63802 14.9036 3.0651 14.0573 2.64844 13.0938C2.23698 12.1302 2.03125 11.099 2.03125 10C2.03125 8.90104 2.23698 7.86979 2.64844 6.90625C3.0651 5.94271 3.63802 5.09635 4.36719 4.36719C5.09635 3.63281 5.94271 3.0599 6.90625 2.64844C7.86979 2.23698 8.90104 2.03125 10 2.03125C11.099 2.03125 12.1302 2.23698 13.0938 2.64844C14.0573 3.0599 14.9036 3.63281 15.6328 4.36719C16.362 5.09635 16.9323 5.94271 17.3438 6.90625C17.7604 7.86979 17.9688 8.90104 17.9688 10C17.9688 11.099 17.7604 12.1302 17.3438 13.0938C16.9323 14.0573 16.362 14.9036 15.6328 15.6328C14.9036 16.362 14.0573 16.9323 13.0938 17.3438C12.1302 17.7604 11.099 17.9688 10 17.9688ZM10 16.6406C10.9167 16.6406 11.776 16.4688 12.5781 16.125C13.3802 15.7812 14.0859 15.3047 14.6953 14.6953C15.3047 14.0859 15.7812 13.3802 16.125 12.5781C16.4688 11.776 16.6406 10.9167 16.6406 10C16.6406 9.08333 16.4688 8.22396 16.125 7.42188C15.7812 6.61458 15.3047 5.90885 14.6953 5.30469C14.0859 4.69531 13.3802 4.21875 12.5781 3.875C11.776 3.53125 10.9167 3.35938 10 3.35938C9.08333 3.35938 8.22396 3.53125 7.42188 3.875C6.61979 4.21875 5.91406 4.69531 5.30469 5.30469C4.69531 5.90885 4.21875 6.61458 3.875 7.42188C3.53125 8.22396 3.35938 9.08333 3.35938 10C3.35938 10.9167 3.53125 11.776 3.875 12.5781C4.21875 13.3802 4.69531 14.0859 5.30469 14.6953C5.91406 15.3047 6.61979 15.7812 7.42188 16.125C8.22396 16.4688 9.08333 16.6406 10 16.6406ZM10 13.9375C9.92188 13.9375 9.84635 13.9245 9.77344 13.8984C9.70573 13.8672 9.63021 13.8125 9.54688 13.7344L6.97656 11.25C6.91406 11.1875 6.86719 11.125 6.83594 11.0625C6.80469 10.9948 6.78906 10.9193 6.78906 10.8359C6.78906 10.6797 6.84115 10.5495 6.94531 10.4453C7.05469 10.3411 7.1901 10.2891 7.35156 10.2891C7.52865 10.2891 7.67188 10.3516 7.78125 10.4766L8.96875 11.7422L9.42969 12.2266L9.38281 11.0547V6.66406C9.38281 6.5026 9.44271 6.36198 9.5625 6.24219C9.6875 6.1224 9.83333 6.0625 10 6.0625C10.1719 6.0625 10.3177 6.1224 10.4375 6.24219C10.5625 6.36198 10.625 6.5026 10.625 6.66406V11.0547L10.5781 12.2266L11.0312 11.7422L12.2188 10.4766C12.3281 10.3516 12.4688 10.2891 12.6406 10.2891C12.8021 10.2891 12.9375 10.3411 13.0469 10.4453C13.1562 10.5495 13.2109 10.6797 13.2109 10.8359C13.2109 10.9193 13.1953 10.9948 13.1641 11.0625C13.1328 11.125 13.0885 11.1875 13.0312 11.25L10.4531 13.7344C10.375 13.8125 10.2995 13.8672 10.2266 13.8984C10.1589 13.9245 10.0833 13.9375 10 13.9375Z"
                    fill="#1F1F1F"
                  />
                </svg>
                {{ $t('download') }}
              </div>
              <div
                class="supportInfo foldTitle"
                :class="{ active: isCenterStatus === 2 }"
                style="justify-content: flex-start; margin-top: 4px !important"
                @click="toCenter(2)"
              >
                <div class="iconfont icon-sidebar-support"></div>
                {{ $t('Me_help') }}
              </div>
            </div>
          </div>
        </template>
      </div>
    </vue-perfect-scrollbar>
    <template v-if="[-1, 30, 31].includes(isCenterStatus) || pageType">
      <div class="middleBox" :class="{ toHide: !middleStatus }">
        <div class="titleBox" v-if="!isFileSelect">
          <div
            class="tagicon iconfont"
            :class="['icon-' + listTitleInfo.icon]"
            :style="{ color: listTitleInfo.color }"
            v-if="!['call', 'note', 'import'].includes(selectMenu)"
            v-html="getIconCode(listTitleInfo.iconCode)"
          ></div>
          <div class="name flex-middle">
            <!-- <el-tooltip effect="dark" :content="listTitleInfo.name" placement="bottom"> -->
            <div>{{ getNameEllipse(listTitleInfo.name, 20) }}</div>
            <!-- </el-tooltip> -->
            ({{ infoList.length }})
          </div>
          <el-tooltip effect="dark" :content="$t('more_actions')" placement="bottom">
            <div class="sortBox" :class="{ active: showSort }">
              <el-popover
                popper-class="elPopoverClass"
                @show="showSelectOrder"
                @hide="showSort = false"
                placement="bottom-end"
                width="216"
                :visible-arrow="false"
                trigger="click"
              >
                <ul class="menuSList">
                  <li @click="handleMoreOptionsSelectClick">
                    <div class="flex-middle">
                      <span class="iconfont gou icon-selected"></span>
                      {{ $t('Filedetail_summary_textfunction_buttonfive') }}
                    </div>
                    <span class="iconfont icon-select"></span>
                  </li>
                  <li class="line"></li>
                  <li
                    v-for="info in orderList"
                    :key="info.id"
                    :class="{ active: sort_by === info.id }"
                    @click="doBySort(1, info)"
                  >
                    <div class="flex-middle">
                      <span class="iconfont gou icon-selected"></span>
                      {{ info.name }}
                    </div>
                    <span
                      class="iconfont"
                      style="font-size: 20px"
                      :class="[info.is_desc === 0 ? 'icon-Ascending' : 'icon-descending']"
                    ></span>
                  </li>
                </ul>
                <template v-slot:reference>
                  <div class="iconfont ricon icon-folder_more"></div>
                </template>
              </el-popover>
            </div>
          </el-tooltip>
        </div>
        <div class="selectedBox" v-else>
          <div class="left">
            <div
              class="lsCheck"
              @click="checkAllFile(true)"
              :class="{ active: selectIds.length == infoList.length }"
            ></div>
            {{ getTranslateStr('Filelist_selected_currentselect', '20240327', selectIds.length) }}
            <span @click="checkAllFile(false)">{{ $t('Filelist_selected_done') }}</span>
          </div>
          <div class="flex-middle rightIcon" v-if="selectIds.length > 0 && selectMenu == 'trash'">
            <el-tooltip effect="dark" :content="$t('Web_sidebar_trash_restore')" placement="bottom">
              <div class="iconfont icon-icon_restore ricon" @click="recoverFile()"></div>
            </el-tooltip>
            <el-tooltip effect="dark" :content="$t('Web_sidebar_trash_delete')" placement="bottom">
              <div class="iconfont icon-icon_delete_permanently ricon" @click="emptyFile()"></div>
            </el-tooltip>
          </div>
          <div class="flex-middle rightIcon" v-else-if="selectIds.length > 0">
            <el-tooltip
              effect="dark"
              :content="$t('Filelist_selected_merge_button')"
              placement="bottom"
              :disabled="mergeIconDisable"
            >
              <div class="w-6 h-6">
                <MergeIcon :clickable="!mergeIconDisable" @click="doMergeFile" />
              </div>
            </el-tooltip>
            <el-tooltip
              effect="dark"
              :content="$t('Filelist_selected_move_button')"
              placement="bottom"
            >
              <div class="iconfont icon-a-moveto ricon" @click="doMoveTag()"></div>
            </el-tooltip>
            <el-tooltip effect="dark" :content="$t('Filelist_selected_trash')" placement="bottom">
              <div class="iconfont icon-trash ricon" @click="moveToTrash()"></div>
            </el-tooltip>
          </div>
        </div>
        <!-- 骨架屏 -->
        <ul class="fileList file-skeleton custom-scrollbar" v-if="isWholeLoading">
          <li class="skeleton-item" v-for="item in 9" :key="item">
            <div class="fileInfo">
              <el-skeleton animated>
                <template slot="template">
                  <el-skeleton-item
                    variant="div"
                    style="width: 70%; height: 20px; margin-bottom: 4px"
                  />
                  <el-skeleton-item
                    variant="div"
                    style="width: 85%; height: 14px; margin-bottom: 5px"
                  />
                  <el-skeleton-item variant="div" style="width: 55%; height: 14px" />
                </template>
              </el-skeleton>
            </div>
          </li>
        </ul>

        <div
          v-if="!isWholeLoading && infoList.length === 0"
          class="flex flex-col pt-[150px] justify-center items-center text-[14px]"
        >
          <img :src="noDataImg" class="w-16 h-16 mb-3" />
          <div class="">{{ $t('Web_no_data_tip') }}</div>
          <div
            v-if="!PPCStatus && selectMenu != 'trash'"
            @click="onPPCDialog()"
            class="text-[#007AFF] cursor-pointer flex items-center px-4"
          >
            <div class="text-center">{{ $t('Web_no_data_ppc_tip') }}</div>
            <span class="ml-1 iconfont icon-help-circle"></span>
          </div>
        </div>

        <RecycleScroller
          v-if="!isWholeLoading && infoList.length != 0"
          ref="fileBox"
          class="fileList custom-scrollbar"
          :items="infoList"
          :item-size="fileItemSize"
          key-field="id"
          :prerender="10"
          v-slot="{ item: info }"
          @update="onUpdate"
          :emitUpdate="true"
        >
          <li
            :key="info.id"
            draggable="true"
            :data-file-id="info.id"
            :ref="`file-card-${info.id}`"
            @dragstart="handleDragStart($event, info)"
            :class="{
              checked: selectIds.includes(info.id),
              active: isFileSelect,
              selected: isSelected(info.id),
            }"
          >
            <div
              class="lsCheck"
              :class="{ active: selectIds.includes(info.id), 'opacity-50': info.isVirtual }"
              @click="selectFile(info)"
            ></div>
            <div
              class="fileInfo"
              @click="selectDetail(info)"
              @contextmenu.prevent="showContextMenu(info)"
            >
              <div class="title" :class="{ 'title-gray': info.isVirtual }">
                {{ info.filename }}
              </div>
              <div class="time_date">
                <div class="iconfont icon-a-Filelist-time"></div>
                {{ getDateStr(info.start_time) }}
                <div
                  class="iconfont icon-a-Filelist-time2"
                  style="margin-left: 12px; margin-right: 2px"
                ></div>
                {{ info.duration < 1000 ? '1s' : getTimeName(getTimeStr(info.duration, false)) }}
              </div>
              <div class="flex-between" style="max-width: 100%">
                <div class="flex-middle" style="flex: 1; width: 0; height: 18px">
                  <div v-html="getComesTag(info)"></div>
                  <div class="ellipsis" v-html="getFileTagHtml(info)"></div>
                </div>
                <div
                  v-if="fileIsLoading(info.id)"
                  class="fileToTrans"
                  :class="[fileIsLoading(info.id)]"
                >
                  <div class="myLoad">
                    <div class="load"></div>
                    {{ $t('Filelist_generating') }}
                  </div>
                  <div class="finshed">
                    <div class="iconfont icon-files_status_generated"></div>
                    {{ $t('Note_status_ai_generated') }}
                  </div>
                </div>
                <mergeProcess v-if="info.isVirtual" :progress="mergePercent" />
              </div>
            </div>
          </li>
        </RecycleScroller>
        <!-- 自定义菜单，根据 showMenu 状态显示或隐藏 -->
        <context-menu
          ref="contextMenu"
          @select="onSelect"
          @move="onMove"
          @delete="onDelete"
          @rename="onRename"
          @trashRestore="onTrashRestore"
          @trashDelete="onTrashDelete"
        />
        <!-- <Loading :status="isLoading" /> -->
      </div>
      <div class="rightBox relative" id="rightBox">
        <!-- <Loading :status="editorLoading" /> -->
        <el-tooltip
          v-if="!pageType"
          effect="dark"
          :content="middleStatus ? $t('Web_filedetail_hidelist') : $t('Web_filedetail_showlist')"
          placement="right"
        >
          <div class="middleHide" @click="changeSmallStatus(2, !middleStatus)">
            <div class="iconfont" :class="[middleStatus ? 'icon-right' : 'icon-next']"></div>
          </div>
        </el-tooltip>

        <div
          v-if="selectFileInfo.id && !pageType"
          class="temp-right-icons"
          :class="{ free: hasFreeShow && userInfo.membership_id }"
        >
          <div class="rightIcons" v-if="selectMenu === 'trash' && !pageType">
            <el-tooltip effect="dark" :content="$t('Web_sidebar_trash_restore')" placement="bottom">
              <div class="iconfont myIcon icon-icon_restore" @click="recoverFile(true)"></div>
            </el-tooltip>
            <el-tooltip effect="dark" :content="$t('Web_sidebar_trash_delete')" placement="bottom">
              <div
                class="iconfont icon-icon_delete_permanently myIcon"
                @click="emptyFile(true)"
              ></div>
            </el-tooltip>
          </div>
          <FileShare @doMenu="doMenu" v-else-if="editorLoaded" />
        </div>
        <TranSummary
          v-if="editorLoaded"
          ref="TranSummary"
          @retran="reTranInfo"
          @preview="handlePreviewTemplate"
          @createTemplate="handleCreateTemplate"
        />
        <!-- @closeDialog="closePreviewOnly" -->

        <Vue3Wrapper
          v-if="selectFileInfo.id || pageType"
          :data="selectFileInfo"
          :onReady="onReady"
          :onChange="onChange"
          :generateStatus="generateStatus"
          :eventType="eventType"
          :userBaseInfo="{ ...ask, ...userInfo }"
          :userInfo="userStateInfo"
          :allFiles="infoList"
          :myFiles="infoAllList"
          :pageType="pageType"
          :pageParams="pageParams"
          :leftSmallStatus="leftSmallStatus"
        />
        <div class="nofile" v-if="!selectFileInfo.id && !pageType">
          <div class="noCont">
            <div class="welTitle">
              <div class="welIcon"></div>
              {{ $t('Welcome_title') }}
            </div>
            <template v-if="gptLabelTxt">
              <div class="yhIcon"></div>
              <div class="label">{{ gptLabelTxt }}</div>
              <div class="gpttip">{{ $t('Welcome_tips') }}</div>
            </template>
          </div>
        </div>
      </div>
    </template>

    <Vue3Wrapper
      v-else-if="isCenterStatus === 0 || isCenterStatus === 4 || isCenterStatus === 5"
      :data="selectFileInfo"
      :onReady="onReady"
      :onChange="onChange"
      :generateStatus="generateStatus"
      :eventType="
        isCenterStatus === 4
          ? 'usercenter_hour'
          : isCenterStatus === 5
            ? 'usercenter_detail'
            : 'usercenter'
      "
      :userInfo="userStateInfo"
      :userBaseInfo="{ ...ask, ...userInfo }"
      style="flex: 1"
    />
    <Download v-else-if="isCenterStatus === 101" @goUpdates="toCenter(103)" />
    <DesktopGuide
      v-else-if="isCenterStatus === 102"
      @goDownLoad="toCenter(101)"
      :desktopPosition="desktopPosition"
    />
    <UpdateLog v-else-if="isCenterStatus === 103" />
    <div class="userCenterWrap" v-else>
      <UserCenter ref="userCenter" :flag="isCenterStatus" />
    </div>

    <EditTag ref="editTag" @ok="getTagList" />
    <MoveTag ref="moveTag" @addTag="addTag" @okMove="okMoveFileTag" />
    <FileUpload ref="FileUpload" v-if="preloaded" />
    <!-- <Loading :status="isWholeLoading" /> -->

    <!-- needMore弹框 -->
    <MemberModal
      :visible="isMemberModalShow"
      width="380px"
      :title="$t('member_transcription_alert_title')"
      :content="getTranslateStr('member_transcription_alert_text')"
      :confirmButtonText="$t('member_dict_got_it')"
      @close="isMemberModalShow = false"
      @confirm="handleMemberModalConfirm"
    >
    </MemberModal>
    <!-- needMore弹框 -->
    <Vue3Wrapper
      :data="toShowFreeData"
      :onChange="onChange"
      page-type="free"
      :userInfo="userStateInfo"
      :userBaseInfo="userInfo"
      v-if="toShowFreePop"
    />
    <!--    <FreeTrial ref="UserUpgrade" @close="showUpgradeModel" v-if="hasFreeShow" />-->
    <UnlimitedUpgrade
      ref="UserUpgrade"
      @close="showUpgradeModel"
      v-if="['pro', 'backer'].includes(userStateInfo.membership_type)"
    />
    <UserUpgrade ref="UserUpgrade" @close="showUpgradeModel" v-else />
    <div :style="{ display: 'none' }" v-if="!editorLoaded && preloaded">
      <Vue3Wrapper :data="selectFileInfo" :onChange="onChange" :onReady="onReady" />
      <!-- <TranSummary ref="TranSummary" @retran="reTranInfo" /> -->
      <!-- <FileShare @doMenu="doMenu" /> -->
    </div>
    <!--   不是会员   -->
    <NoMember ref="NoMember" />
    <!--   当前转写超过100小时   -->
    <TransOverDay ref="TransOverDay" />
    <!-- 隐藏的拖动预览模板 -->
    <div ref="dragPreview" class="drag-preview drag-preview-hidden">拖动文件3个</div>
    <!--   支付试用成功提示   -->
    <BuyFreeTrial ref="BuyFreeTrial" />
    <PPCDialog v-if="showPPCDialog" :hasData="infoAllList.length != 0" :onClose="onPPCDialog" />
    <EditFileName
      v-if="showEditName"
      :fileId="selectFileId"
      :fileName="selectFileName"
      @save="handleRenameSave"
    />
    <!-- 添加气泡提示 -->
    <bubble-alert
      v-if="showBubbleAlert"
      :message="bubbleAlertMessage"
      :type="bubbleAlertType"
      @close="handleBubbleClose"
      :auto-close="isBubbleAutoClose"
      custom-class="fixed top-12  transform  z-50"
      :class="leftSmallStatus ? 'left-[64px]' : 'left-[215px]'"
    >
      <!-- showBubbleAlert = false -->
      <template v-if="showSuccessAction || showRetryAction" #action>
        <div
          class="view-link text-[#007AFF] cursor-pointer text-[14px] text-ellipsis overflow-hidden font-semibold bg-[#F6F8FA] px-4 py-1.5 rounded-full text-center ml-2"
          @click="handleViewClick"
        >
          <span v-if="showSuccessAction">{{ $t('Filelist_selected_merge_success_view') }}</span>
          <span v-if="showRetryAction">{{ $t('Filelist_selected_merge_retry') }}</span>
        </div>
      </template>
      <!-- showRetryAction -->
    </bubble-alert>
  </div>
</template>
<script>
import Cookies from 'js-cookie';
import VuePerfectScrollbar from 'vue-perfect-scrollbar';
import Vue3Wrapper from '../common/vue3-wrapper';
import { mapMutations, mapGetters } from 'vuex';
import { storage } from '@/util/storage';
import FileRate from '@/util/FileRate';
import ContextMenu from './common/ContextMenu.vue';
import BubbleAlert from '../common/BubbleAlert.vue';
import MergeIcon from '../common/mergeIcon.vue';
import { launchDesktop, getAndRemoveQueryParam } from '@/util/query';
import { getQueryParam } from '@/util/common';

import { saveVoiceprint } from '../../util/voiceprint';
import { nextTick } from 'vue/src/core/util';
import { isTimestampFormat } from '@/util/format-date';
import formatDate from '@/util/format-date';
import { parseHash } from '@/util/pageConfig';
import { userLevel } from '@/util/common';
import { getSystemInfo } from '@/util/system';
import noDataImg from '@/images/no-data.png';
import { isString } from '../../../../editor/src/core';
export default {
  mixins: [FileRate],
  components: {
    Download: () => import('./common/download/index.vue'),
    FileShare: () => import('./common/FileShare'),
    EditTag: () => import('./common/EditTag'),
    MoveTag: () => import('./common/MoveTag'),
    TranSummary: () => import('./common/TranSummary'),

    // EditTag,
    // MoveTag,
    VuePerfectScrollbar,
    FileUpload: () => import('./common/FileUpload'),
    MemberModal: () => import('../common/modal/MemberModal.vue'),
    UserCenter: () => import('./UserCenter.vue'),
    MemberModalTimeUnit: () => import('../common/modal/MemberModalTimeUnit.vue'),
    MemberModalUpgradeUnit: () => import('../common/modal/MemberModalUpgradeUnit.vue'),
    UserUpgrade: () => import('./common/membertip/ProUpgrade.vue'),
    TransOverDay: () => import('./common/membertip/TransOverDay.vue'),
    UnlimitedUpgrade: () => import('./common/membertip/UnlimitedUpgrade.vue'),
    NoMember: () => import('./common/membertip/NoMember.vue'),
    FreeTrial: () => import('./common/membertip/FreeTrial.vue'),
    BuyFreeTrial: () => import('./common/membertip/BuyFreeTrial.vue'),
    AmLoad: () => import('../common/AmLoad.vue'),
    // MemberModalTimeUnit,
    // MemberModalUpgradeUnit,
    // UserUpgrade,
    Vue3Wrapper,
    DesktopGuide: () => import('./common/DesktopGuide'),
    UpdateLog: () => import('./common/UpdateLog'),
    ContextMenu,
    PPCDialog: () => import('./common/PPCDialog'),
    EditFileName: () => import('./common/EditFileName.vue'),
    BubbleAlert,
    mergeProcess: () => import('../common/mergeProcess.vue'),
    MergeIcon,
  },
  props: {
    isFromDesktop: {
      type: Boolean,
      default: false,
    },
    desktopPosition: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      noDataImg,
      showDownload: false,
      lastParams: null,
      cacheId: '',
      eventType: '',
      // 转录生成中 1 总结生成中 2 转录结束 3 总结结束 4
      generateStatus: 0,
      // editorLoading: false,
      editorLoaded: false,
      preloaded: false,
      isMemberModalShow: false,
      isWholeLoading: false,
      folderStatus: true,
      comesStatus: true,
      isCenterStatus: -1,
      userStateInfo: {},
      userInfo: {},
      searchTxt: '',
      selectMenu: '',
      tagList: [],
      folderTitleList: [
        { id: '', name: this.$t('Filelist_allfiles'), icon: 'Folder', color: '#98A2B3', logid: 0 },
        {
          id: 'uncategorized',
          name: this.$t('Siderbar_uncategorized'),
          icon: 'Uncategorized_20px',
          color: '#98A2B3',
          logid: 4,
        },
        {
          id: 'trash',
          name: this.$t('Siderbar_trash'),
          icon: 'trash_20px',
          color: '#98A2B3',
          logid: 3,
        },
      ],
      defaultList: [
        { id: 'note', name: this.$t('Filelist_comefrom_note'), color: '#4C8EFF', icon: 'note' },
        { id: 'call', name: this.$t('Filelist_comefrom_call'), icon: 'call', color: '#46CF6C' },
        {
          id: 'notepin',
          name: this.$t('Filelist_comefrom_notepin'),
          color: '#1F1F1F',
          icon: 'import',
        },
        {
          id: 'import',
          name: this.$t('Filelist_comefrom_import'),
          color: '#1F1F1F',
          icon: 'import',
        },
        {
          id: 'desktop',
          name: this.$t('Filelist_comefrom_desktop'),
          color: '#1F1F1F',
          icon: 'import',
        },
      ],
      showSort: false,
      sort_by: 'start_time',
      is_desc: 1,
      packageInfo: {
        backer: this.$t('Me_membercard_backer'),
        pro: this.$t('Me_membercard_pro'),
        traffic: this.$t('Me_membercard_quota'),
        free: this.$t('Me_membercard_free'),
        starter: this.$t('Me_membercard_starter'),
        unlimited: this.$t('Me_membercard_unlimited'),
      },
      isFileSelect: false,
      selectIds: [],
      infoAllList: [],
      pageNumber: 0,
      pagesize: 99999,
      initShowNum: 30,
      showNum: 30,
      middleStatus: true,
      leftSmallStatus: false,
      selectFileInfo: {},
      orderList: [
        { id: 'start_time', name: this.$t('Siderbar_sort_conditionone'), is_desc: 0 },
        { id: 'edit_time', name: this.$t('Siderbar_sort_conditiontwo'), is_desc: 0 },
      ],
      statusInfo: {},
      statusTimer: null,
      gptLabelTxt: '',
      resizeTimer: null,
      selectFileId: '',
      featureList: [
        {
          id: 'askai',
          key: 20,
          name: this.$t('Filedetail_editor_AskAI'),
          icon: 'ai',
          color: '#858C9B',
        },
      ],
      pageType: '',
      pageParams: {},
      userLevel,
      ask: {
        globalQuestion: '',
        newAsk: false,
      },
      operatingSystem: '',
      fileItemSize: 95,
      scrollTimer: null,
      toShowFreePop: false,
      toShowFreeData: {},
      selectFileName: '',
      showPPCDialog: false,
      showEditName: false,
      showBubbleAlert: false,
      bubbleAlertMessage: '',
      bubbleAlertType: 'info',
      isBubbleAutoClose: true,
      showSuccessAction: false,
      showRetryAction: false,
      mergePercent: 0,
      isMerging: false,
      newMergeInfo: null,
      latestMergeIds: [],
      pollTimer: null,
      PPCStatus: true,
      statusList: {
        templateCommunityStatus: 21,
      },
    };
  },
  computed: {
    ...mapGetters('auth', ['redDotShow']),
    isUnlimitedUser() {
      return this.userStateInfo.membership_type == 'unlimited';
    },
    hasFreeShow() {
      return (
        !this.userStateInfo.is_free_trial_history &&
        !this.userStateInfo.is_subscribed &&
        this.userStateInfo.membership_type != 'unlimited'
      );
    },
    memberName() {
      let { membership_id } = this.userInfo;
      if (membership_id) {
        return this.packageInfo[this.userStateInfo.membership_type];
      }
      return this.packageInfo.traffic;
    },
    listTitleInfo() {
      if (!this.selectMenu) {
        return this.folderTitleList[0];
      } else if (this.selectMenu == 'trash') {
        return this.folderTitleList[2];
      } else if (this.selectMenu == 'uncategorized') {
        return this.folderTitleList[1];
      } else {
        let tagInfo = this.tagList.find((item) => item.id == this.selectMenu);
        if (tagInfo) {
          return Object.assign({}, tagInfo, { iconCode: tagInfo.icon });
        }
        let defInfo = this.defaultList.find((item) => item.id == this.selectMenu);
        if (defInfo) {
          return Object.assign({}, defInfo, { icon: 'icon-folder-' + defInfo.icon });
        }
      }
      return {};
    },
    infoList() {
      if (this.selectMenu) {
        if (this.selectMenu == 'trash') {
          return this.infoAllList.filter((item) => item.is_trash == true);
        } else {
          let idx = ['call', 'note', 'import', 'notepin', 'desktop'].indexOf(this.selectMenu);
          if (idx > -1) {
            if (this.selectMenu == 'note') {
              return this.infoAllList.filter(
                (item) =>
                  !item.is_trash &&
                  item.scene == 1 &&
                  (!item.serial_number || !item.serial_number.startsWith('880')),
              );
            } else if (this.selectMenu == 'notepin') {
              return this.infoAllList.filter(
                (item) =>
                  !item.is_trash &&
                  item.scene == 1 &&
                  item.serial_number &&
                  item.serial_number.startsWith('880'),
              );
            }
            return this.infoAllList.filter(
              (item) => !item.is_trash && item.scene == [7, 1, 101, 880, 102][idx],
            );
          } else {
            if (this.selectMenu === 'uncategorized') {
              return this.infoAllList.filter(
                (item) =>
                  !item.is_trash && (!item.filetag_id_list || item.filetag_id_list.length === 0),
              );
            } else {
              return this.infoAllList.filter(
                (item) =>
                  !item.is_trash &&
                  item.filetag_id_list &&
                  item.filetag_id_list.includes(this.selectMenu),
              );
            }
          }
        }
      }
      return this.infoAllList.filter((item) => item.is_trash == false);
    },
    totalLeftMin() {
      //当前用户剩余的套餐时间
      return (
        this.getMinNum(this.userInfo.seconds_left) +
        this.getMinNum(this.userInfo.seconds_left_traffic)
      );
    },
    isVisitor() {
      return !this.userInfo.membership_id && !this.userInfo.membership_id_traffic;
    },
    isShowNeedMoreBtn() {
      return (
        this.totalLeftMin < 60 &&
        !this.isVisitor &&
        this.userStateInfo.membership_type != 'unlimited'
      );
    },
    isShowDesktopDownload() {
      return true;
      // 内部白名单 或 公测白名单 可以显示下载按钮
      // return this.$root?.userStateInfo?.is_inner || this.$root?.userStateInfo?.is_outer;
    },
    mergeIconDisable() {
      return this.selectIds?.length < 2;
    },
    membershipIconName() {
      return !userLevel.includes(this.userStateInfo.membership_type)
        ? 'badge_member'
        : 'badge_beta';
    },
    // 获取当前操作系统对应的Ctrl
    ctrlKeyboard() {
      let keyText = 'Ctrl';
      console.log('this.operatingSystem-ctrlKeyboard', this.operatingSystem);
      switch (this.operatingSystem) {
        case 'macOS':
          keyText = '⌘';
          break;
        case 'windows':
          keyText = 'Ctrl';
          break;
        case 'Linux':
          keyText = 'Ctrl';
          break;
        default:
          keyText = 'Ctrl';
          break;
      }
      return keyText;
    },
  },
  created() {
    this.operatingSystem = getSystemInfo();
  },
  mounted() {
    document.addEventListener('click', this.hideContextMenu);
    document.addEventListener('keydown', this.handleKeyDown);
    const profile = getAndRemoveQueryParam('profile');
    if (profile === 'open') {
      this.toCenter(1);
    }
    if (this.isFromDesktop) {
      this.toCenter(102);
    }

    const upgradeflag = sessionStorage.getItem('upgradeflag');
    if (upgradeflag === 'yes') {
      this.toCenter(0);
    }
    this.$nextTick(() => {
      let myHeight = this.$el.clientHeight;
      let num = Math.ceil((myHeight - 70) / 115);
      this.initShowNum = Math.max(this.initShowNum, num + 10);
    });
    this.isWholeLoading = true;
    this.parseURL();

    // setTimeout(() => {
    //   this.getFileList(99999, true);
    // }, 3000);
    let orderInfo = sessionStorage.getItem('plaud_order');
    console.log(orderInfo, 'orderInfo', this.orderList, 222);
    try {
      if (orderInfo) {
        let { id, is_desc } = JSON.parse(orderInfo);
        let myOrder = this.orderList.find((item) => item.id == id);
        if (myOrder) {
          myOrder.is_desc = is_desc;
          this.doBySort(2, myOrder);
        } else {
          orderInfo = null;
        }
      }
    } catch (e) {
      orderInfo = null;
    }
    if (!orderInfo) {
      this.getFileList(99999, true, true);
    }

    this.getUserInfo().then(() => {
      this.autoShowFreePop();
    });
    this.getTagList();
    let famousTxt = Cookies.get('plaudfamoustxt');
    if (famousTxt) {
      this.gptLabelTxt = famousTxt;
    } else {
      this.reqPostInfo('/ai/label', { language: this.$i18n.locale }).then((data) => {
        this.gptLabelTxt = data.label_text;
        Cookies.set('plaudfamoustxt', this.gptLabelTxt, { expires: 1 });
      });
    }
    this.getTaskStatusList();
    window.addEventListener('resize', this.resizePage, false);
    setTimeout(() => {
      this.preloaded = true;
    }, 500);

    // gsy
    // this.goTemplateCommunity();
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizePage, false);
    document.removeEventListener('keydown', this.handleKeyDown);
    if (this.statusTimer != null) {
      clearTimeout(this.statusTimer);
    }
    if (this.resizeTimer != null) {
      clearTimeout(this.resizeTimer);
    }
    if (this.pollTimer) {
      clearTimeout(this.pollTimer);
      this.pollTimer = null;
    }
    this.scrollTimer && window.clearTimeout(this.scrollTimer);
  },
  watch: {
    isShowDesktopDownload(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          // 获取目标元素
          const targetElement = document.getElementById('start_record');
          // 创建 IntersectionObserver 实例
          const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                // 元素进入视口，进行曝光埋点
                this.setGALogEvent('web_start_record_exposure');
                // 这里可以添加实际的埋点代码，例如发送请求到服务器
                // fetch('your-tracking-url', { method: 'POST', body: JSON.stringify({ event: 'exposure' }) });
                // 停止监听，避免重复埋点
                observer.unobserve(entry.target);
              }
            });
          });
          observer.observe(targetElement);
        });
      }
    },
    // pageType(newVal) {
    //   console.log('pageType-newVal:', newVal);
    // },
  },
  methods: {
    ...mapMutations('auth', ['AUTH_SET_REDDOTSHOW', 'AUTH_SET_USERINFO']),
    // 转写弹窗预览
    handlePreviewTemplate(data) {
      this.pageParams = {
        type: 'previewOnly',
        action: 'previewTemplate',
        data,
      };
      // console.log('pageParams set to:', this.pageParams);
    },
    handleCreateTemplate(data) {
      // console.log('handleCreateTemplate-0602:', data);
      this.pageParams = {
        type: 'previewOnly',
        action: 'createTemplate',
        data,
      };
    },
    autoShowFreePop() {
      //starter用户且有试用机会，连续3天弹窗
      if (this.hasFreeShow && this.userStateInfo.membership_type == 'starter') {
        let uid = this.userInfo.id,
          autoStr = localStorage.getItem(`autofreeset_${uid}`),
          autoInfo = {},
          today = formatDate('yyyy-MM-dd', Date.today());
        if (autoStr) {
          autoInfo = JSON.parse(autoStr);
          if (autoInfo.n >= 3) {
            autoInfo.n = 4;
          } else {
            let dplus = (new Date(today) - new Date(autoInfo.d)) / 3600 / 24 / 1000;
            if (dplus > 1) {
              //间隔超过1天，重新计算
              autoInfo = { d: today, n: 1, open: true };
            } else if (dplus === 1) {
              autoInfo.d = today;
              autoInfo.n = autoInfo.n + 1;
              autoInfo.open = true;
            } else {
              autoInfo.open = false;
            }
          }
        } else {
          autoInfo = { d: today, n: 1, open: true };
        }
        localStorage.setItem(`autofreeset_${uid}`, JSON.stringify(autoInfo));
        if (autoInfo.n < 4 && autoInfo.open) {
          this.$pldAnalytics.reportCustomEvent('web_member_auto_free');
          this.showUpgradeModel(true);
        }
      }
    },
    getFreeLeftDay() {
      let { start_time } = this.$root.userInfo;
      let mus = Date.now() / 1000 - start_time;
      let days = Math.ceil(mus / (60 * 60 * 24));
      return Math.max(0, 7 - days + 1);
    },
    initPaymentStatus() {
      // 初始化订单状态
      const urlParams = new URLSearchParams(window.location.search);
      const sessionId = urlParams.get('session_id');
      let hasUse = sessionStorage.getItem('sessionIdUse');
      if (sessionId && !hasUse) {
        //到会员中心
        this.toCenter(0);
      }
    },
    initFrillRedDot() {
      const { email } = this.$root.userInfo;
      const redDotShowStorage = storage.get('redDotShow', email);
      this.AUTH_SET_REDDOTSHOW({
        name: email,
        data: redDotShowStorage,
      });
    },
    onChange(type, data) {
      if (type !== 'jumpToFile' && this.cacheId && this.cacheId !== this.selectFileInfo.id) {
        return;
      }

      if (type === 'again') {
        console.log(this.lastParams, 'this.lastParams');
        this.$set(this.selectFileInfo, 'generateCode', 0);
        this.generateStatus = this.lastGenerateStatus;

        this.lastParams && this.reTranInfo(this.lastParams);
      }

      if (type === 'generateCode') {
        this.$set(this.selectFileInfo, 'generateCode', 0);

        this.generateStatus = 0;
      }
      if (type === 'generate') {
        this.doMenu(data);
      }
      if (type === 'data') {
        Object.assign(this.selectFileInfo, data);
      }
      if (type === 'status') {
        const { status, params } = data;
        this.generateStatus = status;
        const { post_id, ...obj } = params;
        this.reTranInfo(obj, post_id);
      }
      if (type === 'askai') {
        this.handleMemberCheck(5);
      }
      if (type === 'audio_file') {
        if (data.id) {
          //音频剪辑上传的文件
          this.selectLeftMenu('', 0, [data]);
        }
      }
      if (type === 'audio_trim') {
        this.$refs.NoMember && this.$refs.NoMember.handlerVisible(true);
      }
      if (type === 'refreshUserInfo') {
        this.getUserInfo().then(() => {
          if (data == 4) {
            this.$refs.BuyFreeTrial.handlerVisible(true);
          }
        });
      }
      if (type === 'jumpToFile') {
        let fileInfo = this.infoAllList.find((item) => item.id == data);
        if (fileInfo) {
          if (fileInfo.is_trash == true) {
            this.setMessage(this.$t('file_track_failed_1'));
          } else {
            this.selectMenu = '';
            this.isCenterStatus = -1;
            this.middleStatus = true;
            this.selectDetail(fileInfo);
            this.$nextTick(() => {
              setTimeout(() => {
                this.$refs.fileBox.$el.scrollTop = Math.max(
                  0,
                  this.infoAllList.findIndex((item) => item.id === data) * 95 - 30,
                );
              }, 500);
            });
          }
        } else {
          this.setMessage(this.$t('file_track_failed_1'));
        }
      }
      if (type === 'linkFile') {
        console.log('file&&&&&&:', data);
        this.pageType = '';
        this.selectDetail(data);
      }

      if (type === 'closeFreePop') {
        // console.log('closeFreePop is invoked');
        this.toShowFreeData = {};
        this.toShowFreePop = false;
        this.showUpgradeModel(false, 3);
      }
      if (type === 'template_locked') {
        // 弹出会员弹窗，提示用户升级会员
        this.handleMemberCheck(0);
      }
      if (type === 'previewOnly') {
        if (data.action === 'close') {
          // this.pageType = '';
          this.pageParams = {};
          // console.log('this.pageParams-gsy:', this.pageParams);
        } else if (data.action === 'revertTranSummaryDialog') {
          this.$refs.TranSummary.revertTranSummaryDialog();
        } else if (data.action === 'closeTranSummaryDialog') {
          this.$refs.TranSummary.closeTranSummaryDialog();
        } else if (data.action === 'useTemplate') {
          // console.log('data.data-gsy:', data.data);
          this.$refs.TranSummary.useTemplate(data.data);
        } else if (data.action === 'saveCustomTemplateSuccess') {
          this.$refs.TranSummary.saveCustomTemplateSuccess(data.data);
        }
      }
    },
    onReady() {
      this.editorLoaded = true;
    },
    /*
    * flag:0转写，1总结，2转写+总结
    * trans_result: data.data_result,
      ai_content: this.resloveSummaryData(result.markdown),
      ai_content_mul  [{
      "ai_content":ai_content1,
      "category":"for boss"
      }]
    * */
    async saveData(data, id, flag) {
      let trans_result = null,
        trans_ai = '',
        trans_ai_mul = '';
      if (flag === 0) {
        trans_result = data.data_result;
      } else if (flag === 1) {
        trans_ai = data.data_result;
        trans_ai_mul = data.data_result_summ_mul;
      } else if (flag === 2) {
        trans_result = data.data_result;
        trans_ai = data.data_result_summ;
        trans_ai_mul = data.data_result_summ_mul;
      }
      let ai_content,
        header,
        params = {};

      if (trans_ai_mul) {
        const results = JSON.parse(trans_ai_mul);
        ai_content = [];
        results.forEach((result) => {
          if (!header) {
            header = result.header;
          }
          ai_content.push({
            ai_content: this.resloveSummaryData(result.markdown),
            category: result.header?.category,
            summary_id: result.header?.summary_id,
            original_category: result.header?.original_category + (ai_content.length + 1),
          });
        });
      } else if (trans_ai) {
        const result = JSON.parse(trans_ai);
        ai_content = this.resloveSummaryData(result.markdown);
        header = result.header;
      }


      if (trans_result && data.data_others?.embeddings) {
        const transformResult = await saveVoiceprint(
          data.data_others.embeddings,
          id,
          trans_result,
          ai_content,
        );

        if (transformResult) {
          trans_result = transformResult.transcripts;
          if (ai_content) {
            ai_content = transformResult.summary;            
          }
        }
      }

      if (isTimestampFormat(this.selectFileInfo.filename) && header && header.headline) {
        params.filename = header.headline;
      }
      if (trans_result) params.trans_result = trans_result;
      if (ai_content) {
        if (Array.isArray(ai_content)) {
          params.ai_content = JSON.stringify(ai_content);
        } else {
          params.ai_content = ai_content;
        }
      }
      params.support_mul_summ = true;

      /* 保存task_id_info，用于在点赞点踩时传递埋点参数 */
      const task_id_info = {...(this.selectFileInfo?.extra_data?.task_id_info ?? {}), ...( data?.task_id_info ?? {})}
      if(flag === 1 || flag ===2){  //有总结的情况
        //单维总结时保存summary_id
        if(ai_content && !Array.isArray(ai_content)){
          task_id_info.summary_id = header?.summary_id;
        }
      }
      params.extra_data ={ ...(this.selectFileInfo?.extra_data ?? {}), task_id_info};

      // send request
      await this.reqPatchInfo('/file/' + id, params);
    },
    getSummaryInfo(type, subtype = 'system', lang, transAi, id) {
      if (this.cacheId && this.cacheId !== id) {
        return;
      }
      // this.isLoading = true;
      let params = { type: type, post_id: this.postId, type_type: subtype },
        pamInfo = { language: lang };
      if (transAi) {
        pamInfo.llm = transAi;
      }
      // pamInfo.summary_version = 'v2'; //总结带时间戳协议块
      params.info = JSON.stringify(pamInfo);
      params.support_mul_summ = true;

      this.reqPostInfo('/ai/chatllm/' + this.selectFileInfo.id, params)
        .then(async (data) => {
          let status = data.status;
          if (this.myTimer != null) {
            clearTimeout(this.myTimer);
          }
          // if (status != 0 && status != -2) {
          //   this.isLoading = false;
          // }
          if (status == 0) {
            this.myTimer = setTimeout(() => {
              this.getSummaryInfo(type, subtype, lang, transAi, id);
            }, 3000);
          } else if (status < 0) {
            let generateCode;
            if (status == -11 || status == -12 || status == -13) {
              generateCode = status;
            } else if (status == -110) {
              generateCode = -900110;
            } else if (status == -112) {
              generateCode = -900112;
            } else if (status != -2) {
              generateCode = -100102;
            }

            if (status != -2) {
              this.$set(this.selectFileInfo, 'generateCode', generateCode);
            }

            // 不会出现了
            // if (status === -1) {
            //   this.isEmpty = true;
            // }

            if (status === -111) {
              this.summaryFail = status;
            } else if (status === -11) {
              //没有使用自定义模板的权限
              this.summaryFail = status;
            } else if (status === -12) {
              //自定义模板不存在
              this.summaryFail = status;
            } else if (status === -13) {
              //无权限使用当前系统模板
              this.summaryFail = status;
            } else if (status == -2 && data.data_post_id) {
              this.postId = data.data_post_id;
              this.reTryCount++;
              if (this.reTryCount < 6) {
                this.myTimer = setTimeout(() => {
                  this.getSummaryInfo(type, subtype, lang, transAi, id);
                }, 3000);
              } else {
                this.isLoading = false;
                this.$set(this.selectFileInfo, 'generateCode', -100102);
              }
            } else {
              this.setMessage(data.msg);
              this.isLoading = false;
            }
          } else if (status == 1) {
            this.fileTsFinshed = false;
            //成功
            this.getTaskStatusListOnce();
            await this.saveData(data, id, 1);
            if (data.extra_data) {
              this.selectFileInfo.extra_data = data.extra_data;
            }
            this.generateStatus = 0;

            this.$set(this.selectFileInfo, 'generateCode', 0);
          }
        })
        .catch((error) => {
          console.log(
            error.toString(),
            '2222222222222222222222222',
            error.toString().includes('Network Error'),
          );

          let generateCode = -100102;
          if (error.toString().includes('Network Error')) {
            generateCode = -100200;
          }

          this.$set(this.selectFileInfo, 'generateCode', generateCode);
        });
    },
    resloveSummaryData(data) {
      if (data && data.length > 0) {
        data = data.replace('$[audio_start_time]', this.getDateStr(this.selectFileInfo.start_time));
      }
      return data;
    },

    getTransInfo(reload = 0, lang, type, subtype, isSpeaker = false, transAi = '', id) {
      if (this.cacheId && this.cacheId !== id) {
        return;
      }
      let params = { is_reload: reload },
        pamInfo = { language: lang };
      if (type) {
        params.summ_type = type;
        params.summ_type_type = subtype;
      }
      if (isSpeaker) {
        pamInfo.diarization = 1;
      }
      if (transAi) {
        pamInfo.llm = transAi;
      }
      // pamInfo.summary_version = 'v2'; //总结带时间戳协议块
      params.info = JSON.stringify(pamInfo);
      params.support_mul_summ = true;

      this.reqPostInfo('/ai/transsumm/' + id, params)
        .then(async (data) => {
          let status = data.status;

          if (this.myTimer != null) {
            clearTimeout(this.myTimer);
          }
          // if (status < 0 && status != -2) {
          //   this.loadFlag = -1;
          //   this.isLoading = false;
          // }
          if (status == 0) {
            this.myTimer = setTimeout(() => {
              this.getTransInfo(0, lang, type, subtype, isSpeaker, transAi, id);
            }, 3000);
            if (data.data_result && data.data_result.length > 0) {
              this.summaryLoadType = 1;
            }
          } else if (status < 0) {
            let generateCode;
            if (
              status == -1 ||
              status == -3 ||
              status == -5 ||
              status == -6 ||
              status == -7 ||
              status == -110 ||
              status == -112 ||
              status === -11 ||
              status === -12 ||
              status === -13 ||
              status === -8 ||
              status === -111
            ) {
              generateCode = status;
            } else if (status != -2) {
              generateCode = -100101;
            }

            if (status != -2) {
              this.$set(this.selectFileInfo, 'generateCode', generateCode);
            }

            //
            if (
              status === -111 ||
              status === -11 ||
              status === -12 ||
              status === -13 ||
              status === -7
            ) {
              this.summaryFail = status;
              await this.saveData(data, id, 0);
              this.generateStatus = 0;

              // } else if (status === -7) {
              //   //内容太短
              //   this.transFail = status;
            } else if (status == -2 && data.data_post_id) {
              this.reTryCount++;
              if (this.reTryCount < 6) {
                this.myTimer = setTimeout(() => {
                  this.getTransInfo(0, lang, type, subtype, isSpeaker, transAi, id);
                }, 3000);
              } else {
                this.loadFlag = -1;
                this.isLoading = false;

                this.$set(this.selectFileInfo, 'generateCode', -100101);
              }
            } else {
              // this.setMessage(data.msg);
              this.loadFlag = -1;
              this.isLoading = false;
            }
          } else if (status == 1) {
            //成功
            this.showTranNum = 50;

            await this.saveData(data, id, 2);
            if (data.extra_data) {
              this.selectFileInfo.extra_data = data.extra_data;
            }

            this.generateStatus = 0;

            this.$set(this.selectFileInfo, 'generateCode', 0);

            this.isLoading = false;
            this.getTaskStatusListOnce();
            this.getUserInfo(); //刷新积分值
            this.fileTsFinshed = false;
          }
        })
        .catch((error) => {
          console.log(
            error.toString(),
            '22222222222222222222222221',
            error.toString().includes('Network Error'),
          );

          let generateCode = -100101;
          if (error.toString().includes('Network Error')) {
            generateCode = -100200;
          }

          this.$set(this.selectFileInfo, 'generateCode', generateCode);
          console.log(this.selectFileInfo, '22222222222222222222222221111111111111');
        });
    },
    reTranInfo({ flag, lang, type, subtype, isSpeaker, transAi }, postId = new Date().getTime()) {
      this.$set(this.selectFileInfo, 'generateCode', 0);
      // this.$set(this.selectFileInfo, 'generateCode', this.selectFileInfo.generateCode || 0);

      this.lastParams = { flag, lang, type, subtype, isSpeaker, transAi };

      let myTranFn = () => {
        // TODO: fix this
        this.postId = postId;
        this.reTryCount = 0;
        this.isEmpty = false;
        this.summaryFail = 0;
        this.transFail = 0;
        // this.cancalEditMd(1);
        if (flag == 2 || flag == 22) {
          this.summaryLoadType = 1;
          // if (!this.leftShowStatus) {
          //   this.leftShowStatus = true;
          //   this.initStyleInfo();
          // }
          this.setFileTemplateType(this.selectFileInfo.id, type);
          this.generateStatus = 2;
          this.lastGenerateStatus = 2;
          this.getSummaryInfo(type, subtype, lang, transAi, this.selectFileInfo.id);
        } else {
          this.summaryLoadType = 2;
          this.loadFlag = 0;
          // this.isLoading = true;
          // this.showTrans = true;
          // if (!this.showTrans) {
          //   this.changeShowTrans(true);
          // }
          this.setFileTemplateType(this.selectFileInfo.id, type, true);
          this.getTransInfo(
            flag == 1 ? 1 : 0,
            lang,
            type,
            subtype,
            isSpeaker,
            transAi,
            this.selectFileInfo.id,
          );
          this.generateStatus = 1;
          this.lastGenerateStatus = 1;
        }
        if (flag < 3) {
          //0,1,2  转写或总结刷新列表状态
          this.getTaskStatusListOnce();
          this.setGALogEvent(flag == 2 ? 'web_summary_summit' : 'web_transcribe_summit', {
            lang,
            scenario: type,
            file: this.selectFileInfo.id,
          });
        }
      };
      if (flag === 0 || flag === 1) {
        this.reqGetInfo('/ai/trans-status')
          .then((data) => {
            if (data.status === 0) {
              let { total_seconds } = data;
              //判断当前用户当天的转写时长是否超过100小时
              let hasNoOut = this.$refs.TransOverDay.handlerVisible(
                true,
                this.selectFileInfo.duration / 1000,
                total_seconds,
              );
              if (hasNoOut) {
                this.getUserInfo().then(() => {
                  //转写时长不够
                  if (
                    this.userStateInfo.membership_type != 'unlimited' &&
                    this.selectFileInfo.duration / 1000 > this.getLeftSec()
                  ) {
                    this.showUpgradeModel(true, 1, {
                      flag: 1,
                      duration: this.selectFileInfo.duration,
                      left: this.getLeftSec(),
                    });
                    return false;
                  } else {
                    myTranFn();
                  }
                });
              }
            }
          })
          .catch((error) => {
            console.log(
              error.toString(),
              '22222222222222222222222221111',
              error.toString().includes('Network Error'),
            );
            let generateCode = -100101;

            if (error.toString().includes('Network Error')) {
              generateCode = -100200;
            } else if (flag === 2) {
              generateCode = -100102;
            } else {
              generateCode = -100101;
            }
            this.$set(this.selectFileInfo, 'generateCode', generateCode);
          });
      } else {
        myTranFn();
      }
    },
    getLeftSec() {
      let { seconds_left, membership_id, membership_id_traffic, seconds_left_traffic } =
        this.userInfo;
      if (!membership_id) {
        seconds_left = seconds_left_traffic;
      } else {
        if (membership_id_traffic) {
          seconds_left += seconds_left_traffic;
        }
      }
      return seconds_left;
    },
    // onData(data) {
    //   Object.assign(this.selectFileInfo, data);
    // },
    doMenu(menu = {}) {
      if (menu.id == 'audiotrim') {
        if (this.isFreeMembership()) {
          this.$refs.NoMember.handlerVisible(true);
        } else {
          this.eventType = 'audiotrim_' + new Date().getTime();
        }
      } else if (menu.id == 'move') {
        this.doMoveTag([this.selectFileInfo.id]);
      } else if (menu.id == 'trash') {
        this.moveToTrash([this.selectFileInfo.id]);
      } else if (menu.id == 'jpeg') {
        // // TODO: 下载图片逻辑
        // if (this.summaryFlag !== 1) {
        //   this.setMessage('switch to mind-map');
        //   return false;
        // }
        // 重复点击
        if (this.eventType === 'downloadMindMap') {
          this.eventType = 'downloadMindMap_';
        } else {
          this.eventType = 'downloadMindMap';
        }
        // this.downloadMap();
      } else {
        if (!this.$root.userStateInfo.membership_type) {
          this.doUpladFiles();
          return;
        }
        let flag = 0;
        if (menu.id == 'retran') {
          flag = 1;
        }
        if (menu.id == 'resum') {
          flag = 2;
        }
        this.$refs.TranSummary.handlerVisible(true, flag, this.selectFileInfo);
      }
    },

    handleMemberModalConfirm() {
      this.isMemberModalShow = false;
    },
    isSelected(id) {
      return this.selectFileInfo.id === id;
    },
    resizePage() {
      if ([...Object.values(this.statusList), 20].includes(this.isCenterStatus)) return;
      if (this.resizeTimer != null) {
        clearTimeout(this.resizeTimer);
      }
      this.resizeTimer = setTimeout(() => {
        this.$nextTick(() => {
          let myHeight = this.$el.clientHeight,
            myWidth = this.$el.clientWidth;
          if (myWidth < 1300) {
            this.leftSmallStatus = true;
            this.setGALogEvent('web_hide_sidebar_auto');
          }
          if (myWidth < 1050 && this.selectFileInfo.id) {
            if (this.middleStatus) {
              this.setGALogEvent('web_hide_filelist_auto');
            }
            this.middleStatus = false;
          }
          if (!this.selectFileInfo.id) {
            if (myWidth < 600) {
              this.middleStatus = false;
            } else {
              this.middleStatus = true;
            }
          }

          this.$root.BUS.$emit('changeLayout');
        });
      }, 300);
    },
    changeSmallStatus(flag, bol) {
      if (flag == 1) {
        if (bol) {
          this.setGALogEvent('web_hide_sidebar_button');
        }
        this.leftSmallStatus = bol;
      } else if (flag == 2) {
        if (!bol) {
          this.setGALogEvent('web_hide_filelist_button');
        }
        this.middleStatus = bol;
      }
      this.$root.BUS.$emit('changeLayout');
    },
    selectLeftMenu(id, flag, uploadList = []) {
      this.pageType = '';
      this.selectMenu = id;
      this.isCenterStatus = -1;
      this.middleStatus = true;
      if (uploadList && uploadList.length > 0) {
        this.infoAllList.unshift(...uploadList);
        let fileBoxRef = this.$refs.fileBox;
        fileBoxRef && (fileBoxRef.$el.scrollTop = 0);
      } else if (id === 'askai') {
        // this.isCenterStatus = -1;
      } else {
        //为了跟APP实时获取文件
        this.getFileList();
      }
      if (flag == 0) {
        this.setGALogEvent('web_click_allfile');
      } else if (flag == 1) {
        this.setGALogEvent('web_click_folder');
      } else if (flag == 2) {
        this.setGALogEvent(`web_comefrom_${id}`);
      } else if (flag == 3) {
        this.setGALogEvent('web_trash');
      } else if (flag == 4) {
        this.setGALogEvent('web_click_uncategorized');
      }

      this.selectIds = [];
      if (
        this.selectFileInfo.id &&
        !this.infoList.some((item) => item.id == this.selectFileInfo.id)
      ) {
        this.selectFileInfo = {};
      }
    },
    doFeature({ id, key }) {
      if (this.isWholeLoading) return;
      switch (id) {
        case 'askai':
          this.ask = {
            globalQuestion: '',
            newAsk: true,
          };
          this.goGlobalAsk();
          // 埋点：点击Ask AI按钮
          this.$pldAnalytics.reportCustomEvent('sidebar_ask_ai');
          break;
        default:
          // 默认情况处理，如果需要的话
          break;
      }
    },
    goTemplateCommunity() {
      this.isCenterStatus = this.statusList.templateCommunityStatus;
      this.middleStatus = false;
      this.pageType = 'templateCommunity';
      this.pageParams = {};
    },
    // 判断会员状态
    isMembership(callback = () => {}) {
      let memberType = this.userStateInfo.membership_type;
      // console.log('memberType:', memberType)
      this.authUser = ['pro', 'unlimited', 'backer'].includes(memberType);
      if (!this.authUser) {
        this.$nextTick(() => {
          this.handleMemberCheck(5);
        });
      } else {
        callback && callback();
      }
    },
    goGlobalAsk() {
      this.isMembership(() => {
        this.isCenterStatus = 20;
        this.middleStatus = false;
        this.pageType = 'chat';
      });
    },
    changeListStatus() {
      this.comesStatus = !this.comesStatus;
      if (this.comesStatus) {
        this.setGALogEvent('web_comefrom_show');
      } else {
        this.setGALogEvent('web_comefrom_hide');
      }
    },
    showSelectOrder() {
      this.showSort = true;
      this.setGALogEvent('web_selection');
    },
    addTag(info = {}, mySelect) {
      //mySelect移动tag传递
      this.$refs.editTag.handlerVisible(true, info, mySelect);
    },
    doMoveTag(mySelect = []) {
      this.tagList.forEach((item) => {
        item.count = this.getTagCount(item.id);
      });
      let tagId = '',
        mySelectIDS = this.selectIds;
      if (mySelect.length > 0) {
        mySelectIDS = mySelect;
      }
      for (let i = 0; i < mySelectIDS.length; i++) {
        let sid = mySelectIDS[i],
          fileInfo = this.infoList.find((item) => item.id == sid);
        let { filetag_id_list } = fileInfo;
        if (filetag_id_list && filetag_id_list.length > 0) {
          if (i === 0) {
            tagId = filetag_id_list[0];
          }
          if (tagId != filetag_id_list[0]) {
            tagId = '-1';
            break;
          }
        } else if (i > 0 && tagId != '') {
          tagId = '-1';
          break;
        }
      }
      this.$refs.moveTag.handlerVisible(true, this.tagList, this.selectIds, tagId, mySelect);
    },
    onUpdate(start, end) {
      // console.log(`Updated range: ${start} to ${end}`);
    },
    async handleMergeFile() {
      try {
        const runningTaskRes = await this.reqGetInfo('/file/combine/running-tasks');
        // console.log(runningTaskRes, 'runningTaskRes');
        if (runningTaskRes?.length === 0) return;
        this.isMerging = true;
        const res = runningTaskRes[0];
        const runningFile = {
          ...res?.file,
          id: 'virtual-merge-file-' + Date.now(),
          is_summary: false,
          is_trans: false,
          is_trash: false,
          isVirtual: true,
        }; // 假设接口返回 { file: {...} }
        if (this.pollTimer) {
          if (runningFile) {
            // 用 getInsertIndex 找到插入位置
            const insertIndex = this.getInsertIndex(runningFile);
            if (insertIndex === -1) {
              this.infoAllList.unshift(runningFile);
            } else {
              this.infoAllList.splice(insertIndex, 0, runningFile);
            }
          }
          return;
        }
        if (runningFile) {
          // 有合并任务，插入虚拟文件
          const insertIndex = this.getInsertIndex(runningFile);
          if (insertIndex === -1) {
            this.infoAllList.unshift(runningFile);
          } else {
            this.infoAllList.splice(insertIndex, 0, runningFile);
          }
          this.startMergePolling(res.task_id);
          return;
        }
      } catch (e) {
        console.log(e);
      }
    },
    async doMergeFile() {
      console.log('do merge file');
      if (this.isMerging) {
        this.handleBubble(true, this.$t('Filelist_selected_merge_exist'), 'info');
        return;
      }
      if (this.mergeIconDisable) {
        this.handleBubble(true, this.$t('Filelist_selected_merge_tip'), 'info');
        return;
      }
      // 计算选中文件的总时长（秒）
      const totalDuration = this.selectIds.reduce((sum, id) => {
        const file = this.infoList.find((item) => item.id === id);
        return sum + (file ? file.duration : 0);
      }, 0);
      // 如果总时长超过50小时（180000秒），弹出提示并终止
      if (totalDuration > 50 * 3600 * 1000) {
        this.handleBubble(true, this.$t('Filelist_selected_merge_duration_limit'), 'info');
        return;
      }
      console.log('调用接口进行合并');
      this.isMerging = true;
      this.mergePercent = 0;

      try {
        console.log('选中的文件数量', this.selectIds.length);
        // 埋点：合并文件数量
        this.$pldAnalytics.reportCustomEvent('filelist_select_merge', {
          num: this.selectIds.length,
        });
        const selectedFiles = this.infoList.filter((item) => this.selectIds.includes(item.id));
        // 找到 start_time 最小的文件
        const lastSelectedFile = selectedFiles.reduce((min, file) => {
          return !min || file.start_time < min.start_time ? file : min;
        }, null);
        const lastSelectedIndex = this.infoList.findIndex(
          (item) => item.id === lastSelectedFile.id,
        );

        // 计算选中文件的总时长
        const totalDuration = this.selectIds.reduce((sum, id) => {
          const file = this.infoList.find((item) => item.id === id);
          return sum + (file ? file.duration : 0);
        }, 0);
        const filename = `${this.getDateStr(lastSelectedFile.start_time)} merged`;

        // 清除当前的选中状态
        this.isFileSelect = false;
        this.latestMergeIds = [...this.selectIds];
        this.selectIds = [];

        // 调用合并接口
        const response = await this.reqPostInfo('/file/combine', {
          file_ids: this.latestMergeIds,
          filename: filename,
        });

        if (response?.status === 0) {
          const newItem = {
            ...this.infoList[lastSelectedIndex],
            id: 'virtual-merge-file-' + Date.now(),
            duration: totalDuration,
            filename,
            filetag_id_list: [],
            is_summary: false,
            is_trans: false,
            is_trash: false,
            isVirtual: true,
            edit_time: new Date().getTime(),
          };

          const insertIndex = this.getInsertIndex(newItem);
          if (insertIndex === -1) {
            this.infoAllList.unshift(newItem);
          } else {
            this.infoAllList.splice(insertIndex, 0, newItem);
          }

          // 如果返回状态为0，开始轮询任务状态
          const taskId = response.task_id;
          this.startMergePolling(taskId);

          // 埋点：文件合并状态-成功
          this.$pldAnalytics.reportCustomEvent('file_merge', {
            status: 'succeed',
          });

          return;
        }
        if (response?.status === -1) {
          this.handleBubble(true, this.$t('Filelist_selected_merge_fail_tip'), 'error');
          this.showRetryAction = true;
        }
        if (response?.status === -2) {
          this.handleBubble(true, this.$t('Filelist_selected_merge_fail_noexist'), 'error');
        }
        if (response?.status === -3) {
          this.handleBubble(true, this.$t('Filelist_selected_merge_fail_notype'), 'error');
        }
        if (response?.status === -4) {
          this.handleBubble(true, this.$t('Filelist_selected_merge_duration_limit'), 'info');
          // 埋点：文件合并上限
          this.$pldAnalytics.reportCustomEvent('file_merge_limit');
        }

        // 埋点：文件合并状态-失败
        this.$pldAnalytics.reportCustomEvent('file_merge', {
          status: 'failed',
        });

        // 其他异常场景需要把当前文件给删除
        this.removeVirtualFile();
        this.isMerging = false;
      } catch (error) {
        this.isMerging = false;
        console.error('Merge files failed:', error);
        this.handleBubble(true, this.$t('Filelist_selected_merge_fail_tip'), 'error');
        this.showRetryAction = true;
        this.removeVirtualFile();
        // 埋点：文件合并状态-失败
        this.$pldAnalytics.reportCustomEvent('file_merge', {
          status: 'failed',
        });
      }
    },
    startMergePolling(taskId) {
      // 先清理已有定时器
      if (this.pollTimer) {
        clearTimeout(this.pollTimer);
        this.pollTimer = null;
      }
      const pollTaskStatus = async () => {
        try {
          const taskResponse = await this.reqGetInfo(`/file/combine-tasks/${taskId}`);
          if (taskResponse?.status === 'processing' || taskResponse?.status === 'pending') {
            this.mergePercent = taskResponse.progress;
            this.pollTimer = setTimeout(pollTaskStatus, 3000);
          } else if (taskResponse?.status === 'success' || taskResponse?.status === 'failed') {
            this.isMerging = false;
            if (this.pollTimer) {
              clearTimeout(this.pollTimer);
              this.pollTimer = null;
            }
            if (taskResponse?.status === 'failed') {
              // 失败处理
              this.handleBubble(true, this.$t('Filelist_selected_merge_fail_tip'), 'error');
              this.showRetryAction = true;
              this.removeVirtualFile();
            }
            if (taskResponse?.status === 'success') {
              this.mergePercent = taskResponse.progress;
              this.handleBubble(true, this.$t('Filelist_selected_merge_success'), 'success');
              this.showSuccessAction = true;
              if (taskResponse.file) {
                const virtualFileIndex = this.infoAllList.findIndex((item) => item.isVirtual);
                if (virtualFileIndex !== -1) {
                  if (!this.infoAllList.find((item) => item.id === taskResponse.file.id)) {
                    this.infoAllList.splice(virtualFileIndex, 1, taskResponse.file);
                  } else {
                    this.infoAllList.splice(virtualFileIndex, 1);
                  }
                  // this.infoAllList.splice(virtualFileIndex, 1, taskResponse.file);
                }
                this.newMergeInfo = taskResponse.file;
              }
            }
          }
        } catch (error) {
          this.isMerging = false;
          if (this.pollTimer) {
            clearTimeout(this.pollTimer);
            this.pollTimer = null;
          }
          this.handleBubble(true, this.$t('Filelist_selected_merge_fail_tip'), 'error');
          this.showRetryAction = true;
          this.removeVirtualFile();
        }
      };
      pollTaskStatus();
    },
    removeVirtualFile() {
      const virtualIndex = this.infoAllList.findIndex((item) => item.isVirtual);
      if (virtualIndex !== -1) {
        this.infoAllList.splice(virtualIndex, 1);
      }
    },
    handleBubble(isshow, message, type) {
      this.showBubbleAlert = isshow;
      this.bubbleAlertMessage = message;
      this.bubbleAlertType = type;
    },
    handleViewClick() {
      console.log(this.showSuccessAction, 'this.showSuccessAction');
      console.log(this.showRetryAction, 'this.showRetryAction');
      // 清除选中状态，防止引发异常情况
      this.clearSelectStatus();
      if (this.showSuccessAction) {
        if (this.newMergeInfo) {
          this.selectLeftMenu('', 0);
          if (!this.checkIsVisible(this.newMergeInfo)) {
            this.scrollFileIntoView(this.newMergeInfo.id);
          }
          this.selectDetail(this.newMergeInfo);
          this.clearBubbleStatus();
        }
        return;
      }
      if (this.showRetryAction) {
        this.handleRetry();
        this.clearBubbleStatus();
      }
    },
    checkIsVisible(info) {
      const el = this.$refs[`file-card-${info.id}`];
      if (el) {
        const rect = el.getBoundingClientRect();
        const fullyInView =
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.right <= (window.innerWidth || document.documentElement.clientWidth);
        console.log(fullyInView, 'fullyInView');
        return fullyInView;
      }
      return false;
    },
    isAllOrUncategorized() {
      return !this.selectMenu || this.selectMenu === 'uncategorized';
    },
    handleBubbleClose() {
      this.clearBubbleStatus();
    },
    clearSelectStatus() {
      this.isFileSelect = false;
      this.selectIds = [];
    },
    clearBubbleStatus() {
      this.showBubbleAlert = false;
      this.showSuccessAction = false;
      this.showRetryAction = false;
    },
    handleRetry() {
      console.log('handleRetry');
      // if (this.selectIds.length === 0) {
      this.selectIds = [...this.latestMergeIds];
      // }
      this.doMergeFile();
    },
    getInsertIndex(newFile) {
      // sort_by: 'start_time' 或 'edit_time'
      // is_desc: 0（升序）或 1（降序）

      const sort_by = this.sort_by; // 'start_time' 或 'edit_time'
      const is_desc = this.is_desc === 1;

      let insertIndex = -1;
      if (sort_by === 'start_time') {
        if (is_desc) {
          const sameTimeIndex = this.infoList.findIndex((item) => {
            // console.log(item, 'item');
            // console.log(newFile, 'newFile');
            return !item.is_trash && newFile[sort_by] === item[sort_by];
          });
          // console.log(sameTimeIndex, 'sameTimeIndex');
          if (sameTimeIndex !== -1) {
            const sameTimeItem = this.infoList[sameTimeIndex];
            const itemInAllListIndex = this.infoAllList.findIndex(
              (item) => item.id === sameTimeItem.id,
            );

            insertIndex = itemInAllListIndex;
          } else {
            // 考虑合并文件被删除的情况 需要找到第一个创建时间小于它的，并且插入到其之前
            const firstItem = this.infoAllList.find(
              (item) => item.start_time < newFile.start_time && !item.is_trash,
            );
            if (firstItem) {
              insertIndex = this.infoAllList.findIndex((item) => item.id === firstItem.id);
            }
          }
        } else {
          const sameTimeIndex = this.infoList.findLastIndex((item) => {
            return !item.is_trash && newFile[sort_by] === item[sort_by];
          });
          if (sameTimeIndex !== -1) {
            const sameTimeItem = this.infoList[sameTimeIndex];
            const itemInAllListIndex = this.infoAllList.findIndex(
              (item) => item.id === sameTimeItem.id,
            );
            insertIndex = itemInAllListIndex + 1;
          } else {
            // 考虑合并文件被删除的情况 需要找到第一个创建时间大于它的，并且插入到其之前
            const lastItem = this.infoAllList.find(
              (item) => item.start_time > newFile.start_time && !item.is_trash,
            );
            if (lastItem) {
              insertIndex = this.infoAllList.findIndex((item) => item.id === lastItem.id);
            }
          }
        }
      }
      if (sort_by === 'edit_time') {
        if (!is_desc) {
          insertIndex = this.infoAllList.length;
        } else {
          insertIndex = 0;
        }
      }
      console.log(insertIndex, 'insertIndex');
      return insertIndex;
    },
    getUserInfo() {
      return this.reqGetInfo('/user/me').then((data) => {
        //seconds_left,seconds_total
        // data.data_state.membership_type = 'pro';
        // 为了屏蔽free trial标识
        // data.data_state.is_free_trial_history = 0;
        // data.data_state.is_subscribed = false;
        // data.data_state.is_free_trial_now = 0;
        // data.data_state.autorenew_status_web = true;
        this.$root.userInfo = this.userInfo = data.data_user;
        this.AUTH_SET_USERINFO(data.data_user);
        this.$root.userStateInfo = this.userStateInfo = data.data_state;
        this.initFrillRedDot();
        this.initPaymentStatus();
      });
    },
    getTagList(delId, count, moveSelect = []) {
      this.getTagInfoList().then((data) => {
        let { data_filetag_total, data_filetag_list } = data;
        this.tagList = data_filetag_list;
        if (moveSelect && moveSelect.length > 0) {
          //移动出发的添加
          if (moveSelect[0] == -1) {
            moveSelect = this.selectIds;
          }
          this.doMoveTag(moveSelect);
        }
      });
      if (delId) {
        if (delId === this.selectMenu) {
          //删除了tag
          this.selectMenu = '';
        }
        if (count && count > 0) {
          //标签下有文件，移入垃圾桶
          let ids = this.infoAllList
            .filter((item) => item.filetag_id_list && item.filetag_id_list.includes(delId))
            .map((item) => item.id);
          if (ids.length > 0) {
            this.moveToTrash(ids, false);
          }
        } else {
          if (delId === this.selectMenu) {
            this.getFileList();
          }
        }
      }
    },
    getFileTagHtml({ filetag_id_list, scene }) {
      let tagInfo = null;
      if (filetag_id_list && filetag_id_list.length > 0 && filetag_id_list[0] != '') {
        tagInfo = this.tagList.find((item) => item.id == filetag_id_list[0]);
      }
      if (tagInfo) {
        return `<div class="tagName" style="color:${
          tagInfo.color
        }"><span class="iconfont">${this.getIconCode(tagInfo.icon)}</span><div class="ellipsis">${
          tagInfo.name
        }</div></div>`;
      }
      return '<div class="tagName"></div>';
    },
    getComesTag({ scene, serial_number }) {
      let tagInfo = null,
        key = '';
      if (scene === 1) {
        if (serial_number && serial_number.startsWith('880')) {
          key = 'notepin';
        } else {
          key = 'note';
        }
      } else if (scene === 7) {
        key = 'call';
      } else if (scene === 101) {
        key = 'import';
      } else if (scene === 102) {
        key = 'desktop';
      }
      tagInfo = this.defaultList.find((item) => item.id == key);

      if (tagInfo) {
        return `<div class="comesTag">${tagInfo.name}</div>`;
      }
      return '';
    },
    getFlipNum(limit) {
      let skip = this.pageNumber * this.pagesize;
      // limit = this.pagesize;
      return `skip=${skip}&limit=${limit}`;
    },
    getConditions() {
      let cond = '&is_trash=2';
      cond += `&sort_by=${this.sort_by}`;
      cond += `&is_desc=${this.is_desc == 1 ? true : false}`;
      return cond;
    },
    // 指定文件滚动到可视区域
    scrollFileIntoView(fileId) {
      this.scrollTimer && window.clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        const index = this.infoList.findIndex((item) => item.id === fileId);
        this.$refs.fileBox.scrollToPosition(Math.max(0, index) * this.fileItemSize);
      }, 1000);
    },
    async getFileList(limit = 99999, hideLoading = false, checkPPC = false) {
      // 先把虚拟文件删掉，再进行添加；不然页面会闪一下
      if (this.isAllOrUncategorized()) {
        // this.infoList = this.infoAllList;
        const deleteIndex = this.infoAllList.findIndex((item) => item.isVirtual);
        if (deleteIndex !== -1) {
          this.infoAllList.splice(deleteIndex, 1);
        }
      }
      if (!this.isWholeLoading && !hideLoading) {
        this.isLoading = true;
      }
      this.selectIds = [];
      this.showNum = this.initShowNum;
      try {
        const promises = [
          this.reqGetInfo('/file/simple/web?' + this.getFlipNum(limit) + this.getConditions()),
        ];
        if (checkPPC) {
          promises.push(this.reqGetInfo('/user/me/settings'));
        }
        const [data, ppcData] = await Promise.all(promises);
        // const data = await this.reqGetInfo(
        //   '/file/simple/web?' + this.getFlipNum(limit) + this.getConditions(),
        // );
        let { data_file_list, data_file_total } = data;
        if (
          this.selectFileInfo.id &&
          !data_file_list.some((item) => item.id == this.selectFileInfo.id)
        ) {
          this.selectFileInfo = {};
        }
        this.infoAllList = data_file_list;
        this.isLoading = false;
        this.isWholeLoading = false;
        if (checkPPC && ppcData && !ppcData.ppc_status) {
          if (!localStorage.getItem('CHECK_PPC')) {
            this.showPPCDialog = true;
          }
          this.PPCStatus = ppcData.ppc_status;
        }
        // 进入文件详情，文件块滚动到可视区域
        parseHash('file_detail', () => {
          let dataStr = getQueryParam(window.location.href, 'data');
          let file = JSON.parse(dataStr);
          this.scrollFileIntoView(file.id);
        });
        // 处理合并文件逻辑
        this.handleMergeFile();
      } catch (error) {
        console.error('Error fetching file list:', error);
        this.infoAllList = [];
        this.isWholeLoading = false;
      }
    },
    getTagCount(id) {
      return this.infoAllList.filter(
        (item) =>
          item.filetag_id_list && item.is_trash == false && item.filetag_id_list.includes(id),
      ).length;
    },
    getWholeCount(flag) {
      if (flag === 0) {
        //all
        return this.infoAllList.filter((item) => item.is_trash == false && !item.isVirtual).length;
      } else if (flag === 3) {
        //trash
        return this.infoAllList.filter((item) => item.is_trash == true).length;
      } else if (flag === 4) {
        //uncategorized
        return this.infoAllList.filter(
          (item) =>
            item.is_trash == false &&
            (!item.filetag_id_list || item.filetag_id_list.length === 0) &&
            !item.isVirtual,
        ).length;
      }
      return 0;
    },
    handleMoreOptionsSelectClick() {
      // 埋点：点击更多选择
      this.$pldAnalytics.reportCustomEvent('filelist_more_select');
      this.doBySort(3);
    },
    doBySort(flag, info) {
      if (flag != 3) {
        if (this.sort_by != info.id || flag == 2) {
          this.sort_by = info.id;
          this.is_desc = info.is_desc;
          if (info.id == 'start_time') {
            this.setGALogEvent('web_sort_create');
          } else {
            this.setGALogEvent('web_sort_edit');
          }
        } else {
          this.$set(info, 'is_desc', info.is_desc === 0 ? 1 : 0);
          this.is_desc = info.is_desc;
          if (this.is_desc === 1) {
            this.setGALogEvent('web_sort_down');
          } else {
            this.setGALogEvent('web_sort_up');
          }
        }
        sessionStorage.setItem('plaud_order', JSON.stringify(info));
        this.getFileList();
      } else {
        this.isFileSelect = true;
        this.setGALogEvent('web_mutiple_select');
        if (info.id) {
          this.selectFile(info);
        }
      }
      document.body.click();
    },
    selectFile({ id, isVirtual }) {
      console.log('selectFile', id);
      if (isVirtual) {
        return;
      }
      let idx = this.selectIds.indexOf(id);
      if (idx > -1) {
        this.selectIds.splice(idx, 1);
      } else {
        this.selectIds.push(id);
      }
    },
    selectDetail(info) {
      if (info.isVirtual) {
        return;
      }
      // 如果是选择模式，点击文件，则进行多选，而不是打开文件内容
      if (this.isFileSelect) {
        this.selectFile(info);
        return;
      }
      // if (!this.editorLoaded) {
      //   this.editorLoading = true;
      // }
      console.log('selectDetail', info, this.selectFileInfo);
      this.cacheId = info.id;
      this.selectFileInfo = info;
      this.generateStatus = 0;
      this.$set(this.selectFileInfo, 'generateCode', info.generateCode || 0);

      this.setGALogEvent('web_filedetail_click');
    },
    checkAllFile(bol = true) {
      let ids = [];
      if (this.selectIds.length < this.infoList.length && bol) {
        this.infoList.forEach((item) => {
          ids.push(item.id);
        });
      }
      this.selectIds.splice(0, this.selectIds.length, ...ids);
      this.isFileSelect = bol;
    },
    moveToTrash(delIDs = this.selectIds, showTip = true) {
      this.isLoading = true;
      this.reqPostInfo('/file/trash/', delIDs).then((data) => {
        if (showTip) {
          this.setMessage(this.$t('Filelist_selected_trash_toast'));
        }
        this.setGALogEvent('web_move_trash');
        if (delIDs.includes(this.selectFileInfo.id)) {
          this.selectFileInfo = {};
        }
        this.getFileList();
        // 处理成功后，退出多选模式
        this.isFileSelect = false;
      });
    },
    recoverFile(isDetail = false, fileId) {
      let ids = this.selectIds;
      if (isDetail) {
        ids = [this.selectFileInfo?.id || fileId];
      } else {
        this.isLoading = true;
      }
      this.reqPostInfo('/file/untrash/', ids).then((data) => {
        this.showSuccessMsg(this.$t('Web_sidebar_trash_restore_toast'));
        if (isDetail) {
          this.selectMenu = '';
        }
        this.setGALogEvent('web_trash_restore');
        this.getFileList();
        // 处理成功后，退出多选模式
        this.isFileSelect = false;
      });
    },
    emptyFile(isDetail = false, fileId) {
      let ids = this.selectIds;
      if (isDetail) {
        ids = [this.selectFileInfo.id || fileId];
      }
      this.setGALogEvent('web_trash_delete');
      this.setConfirm({
        title: this.$t('Sidebar_trash_delete_title'),
        msg: this.getTranslateStr('Sidebar_trash_deleted_content'),
        okname: this.$t('Sidebar_trash_deleted_confirm'),
        ok: () => {
          this.isLoading = !isDetail;
          this.reqDeleteInfo('/file/', ids).then((data) => {
            this.setMessage(this.$t('Sidebar_trash_deleted_toast'));
            if (isDetail) {
              this.selectMenu = '';
              this.selectFileInfo = {};
            }
            this.setGALogEvent('web_trash_delete_success');
            this.getFileList();
            // 处理成功后，退出多选模式
            this.isFileSelect = false;
          });
        },
      });
    },
    okMoveFileTag(tagId, mySelect = []) {
      if (
        this.tagList.some((item) => item.id == this.selectMenu) &&
        this.selectMenu != tagId &&
        mySelect.length == 0
      ) {
        this.getFileList();
      } else {
        let mySelectIds = this.selectIds;
        if (mySelect.length > 0) {
          mySelectIds = mySelect;
        }
        mySelectIds.forEach((id) => {
          let fileInfo = this.infoList.find((item) => item.id == id);
          if (tagId == '') {
            fileInfo.filetag_id_list.splice(0, fileInfo.filetag_id_list.length);
          } else {
            fileInfo.filetag_id_list.splice(0, fileInfo.filetag_id_list.length, tagId);
          }
        });
        if (mySelect.length > 0) {
          this.$forceUpdate();
        }
        mySelectIds.splice(0, mySelectIds.length);
      }
      // 处理成功后，退出多选模式
      this.isFileSelect = false;
    },
    load() {
      let ps = this.$refs.fileBox.$el;
      if (
        this.showNum < this.infoList.length &&
        ps.scrollTop >= ps.scrollHeight - ps.clientHeight - 5
      ) {
        this.showNum += 10;
      }
    },
    toCenter(flag = 0) {
      this.pageType = '';
      if (flag != 2) {
        this.setGALogEvent('web_view_me_page');
      }
      this.isCenterStatus = flag;
      this.selectFileInfo = {};
      if (flag === 0 || flag == 4 || flag == 5) {
        this.getUserInfo();
      } else {
        // TODO 逻辑优化
        // this.$nextTick(() => {
        //   if(this.$refs.userCenter && this.$refs.userCenter.goTo){
        //     this.$refs.userCenter.goTo(flag);
        //   }
        //   else{
        //     setTimeout(() => {
        //       this.$refs.userCenter.goTo(flag);
        //     }, 400);
        //   }
        // });
      }
    },
    getTaskStatusList() {
      if (this.statusTimer != null) {
        clearTimeout(this.statusTimer);
      }
      this.reqGetInfo('/ai/status').then((data) => {
        this.statusInfo = data;
        this.statusTimer = setTimeout(this.getTaskStatusList, 10000);
      },()=>{
        this.statusTimer = setTimeout(this.getTaskStatusList, 5000);
      });
    },
    getTaskStatusListOnce() {
      this.reqGetInfo('/ai/status').then((data) => {
        this.statusInfo = data;
      });
    },
    fileIsLoading(fileId) {
      const {
        data_processing,
        data_processing_chatllm,
        data_processing_transsumm,
        data_processing_ai,
        data_processing_chatllm_ai,
        data_processing_transsumm_ai,
      } = this.statusInfo;

      const includesFileId = (list, key) => list && list[key] && list[key].includes(fileId);
      const someFileId = (list) => list && list.some((item) => item.file_id === fileId);

      const isProcessing =
        (data_processing && data_processing.includes(fileId)) ||
        includesFileId(data_processing_transsumm, 'files_trans') ||
        includesFileId(data_processing_transsumm, 'files_summ') ||
        someFileId(data_processing_chatllm);

      const isProcessingAI =
        (data_processing_ai && data_processing_ai.includes(fileId)) ||
        includesFileId(data_processing_transsumm_ai, 'files_trans') ||
        includesFileId(data_processing_transsumm_ai, 'files_summ') ||
        someFileId(data_processing_chatllm_ai);

      if (isProcessing) {
        return isProcessingAI ? 'fileFinshed' : 'fileLoading';
      }

      return '';
    },
    async onRecord() {
      this.isCenterStatus = 30;
      this.setGALogEvent('web_lanunch_record');
      const result = await launchDesktop(1);
      console.log(result);
      if (result === 'failed') {
        this.toCenter(101);
      }
    },

    doUpladFiles() {
      this.isCenterStatus = 31;
      //此方法改动的话需要注意，多个地方调用
      this.$refs.FileUpload && this.$refs.FileUpload.handlerVisible(true);
    },
    showUpgradeModel(status, flag, info = {}) {
      if (status) {
        if (!!this.$refs.TranSummary) {
          this.$refs.TranSummary.upgradeStatus = true;
        }
        let eventName = [
            '',
            'web_member_less_transtime',
            'web_member_less_template',
            'web_member_less_templatedefine',
            'web_member_less_transtime',
            'web_member_less_askai',
          ][flag],
          evt_tag = 'upgrade';
        info.flag = flag;
        if (this.hasFreeShow) {
          this.toShowFreeData = info;
          this.toShowFreePop = true;
          evt_tag = 'free';
        } else {
          this.$refs.UserUpgrade.handlerVisible(true, info);
        }
        if (eventName) {
          this.$pldAnalytics.reportCustomEvent(`${eventName}_${evt_tag}`);
        }
      } else {
        if (!!this.$refs.TranSummary) {
          this.$refs.TranSummary.upgradeStatus = false;
          if (flag !== 3) {
            this.$refs.TranSummary.handlerVisible(false);
          }
        }
        if (flag === 2) {
          this.toCenter(5);
        } else if (flag === 3) {
          //只是关闭弹窗
        } else {
          this.toCenter(0);
        }
      }
    },
    isBasicMembership() {
      return !['backer', 'unlimited'].includes(this.userStateInfo.membership_type);
    },
    isFreeMembership() {
      return !['pro', 'backer', 'starter', 'unlimited'].includes(
        this.userStateInfo.membership_type,
      );
    },
    handleMemberCheck(flag, type = '') {
      if (type === 'userset') {
        this.showUpgradeModel(true, flag);
        return;
      }
      //user点击升starter，starter点击升pro
      if (this.isFreeMembership()) {
        this.$refs.NoMember.handlerVisible(true);
      } else if (this.isBasicMembership() || this.hasFreeShow) {
        this.showUpgradeModel(true, flag);
      }
    },
    getImagePath(icon) {
      try {
        return require(`../../../public/static/${icon}.svg`);
      } catch (e) {
        console.error(`Image not found: ../../../public/static/${icon}.svg`, e);
        return '';
      }
    },
    // 文件拖拽开始
    handleDragStart(event, item) {
      if (item.is_trash || item.isVirtual) {
        event.preventDefault();
        return;
      }
      // 设置拖动预览（使用隐藏的 dragPreview 元素）
      const preview = this.$refs.dragPreview;
      let num = 0;
      if (this.isFileSelect && this.selectIds.length > 0) {
        num = this.selectIds.length;
      } else {
        num = 1;
      }
      if (preview) {
        preview.classList.remove('drag-preview-hidden');
        preview.classList.add('drag-preview-visible');
        preview.innerHTML = `${num}${num === 1 ? this.$t('file_selected') : this.$t('files_selected')}<br>${this.$t('move_to_folder')}`; // 动态更新内容

        // 设置拖动预览图像
        event.dataTransfer.setDragImage(preview, 60, 0); // 偏移量为 10px, 10px
      }
      event.dataTransfer.setData('text/plain', JSON.stringify(item)); // 传递文件ID
      // filename
      event.dataTransfer.effectAllowed = 'move';
      this.setGALogEvent('web_move_folder_from_drag');
    },
    // 拖动经过目标区域时触发
    handleDragOver(event, folder) {
      event.preventDefault(); // 必须阻止默认行为以允许drop
      console.log('handleDragOver', event);
      if (this.$refs[`tag-${folder.id}`][0].style.backgroundColor !== '#dee2e9') {
        this.$refs[`tag-${folder.id}`][0].style.backgroundColor = '#dee2e9';
      }
    },
    // 拖动进入目标区域时触发（可选，用于样式变化）
    handleDragEnter(event, folder) {
      event.preventDefault();
    },
    // 放置时触发
    handleDrop(event, folder) {
      event.preventDefault();
      let fileArr = [];
      let fileTagList = [];
      this.$refs[`tag-${folder.id}`][0].style.backgroundColor = '';
      if (this.isFileSelect && this.selectIds.length > 0) {
        fileArr = this.selectIds;
        // 如果文件已经存在，则需要做吐司处理
        const tagArr = [];
        this.infoAllList.forEach((info) => {
          if (this.selectIds?.includes(info?.id) && !tagArr.includes(info.filetag_id_list[0])) {
            tagArr.push(info.filetag_id_list[0]);
          }
        });
        if (tagArr.length === 1 && tagArr[0] === folder.id) {
          this.setMessage(this.$t('files_exist_tip'));
          return;
        }
      } else {
        const fileItem = event.dataTransfer.getData('text/plain'); // 获取拖动的文件ID
        const fileId = JSON.parse(fileItem).id;
        fileTagList = JSON.parse(fileItem)?.filetag_id_list || [];
        fileArr = [fileId];
        // const fileName = JSON.parse(fileItem).filename;
        // 如果文件已经存在，则需要做吐司处理
        if (fileTagList.includes(folder.id)) {
          this.setMessage(this.$t('file_exist_tip'));
          return;
        }
      }
      this.updateFilesTag(fileArr, folder.id)
        .then((data) => {
          // this.$emit('okMove', tagId, this.mySelect);
          this.okMoveFileTag(folder.id, fileArr);
          this.setGALogEvent('web_move_folder_from_drag_success');
          this.setMessage(this.getTranslateStr('move_success', '20240327', folder.name));
        })
        .catch(() => {
          this.setMessage(this.$t('move_fail'));
        });
    },
    handleDragLeave(event, folder) {
      console.log('handleDragLeave', event.target);
      event.preventDefault();
      this.$refs[`tag-${folder.id}`][0].style.backgroundColor = '';
    },
    showContextMenu(info) {
      if (this.isFileSelect || info.isVirtual) {
        return;
      }
      event.preventDefault(); // 阻止默认右键菜单
      const { clientX, clientY } = event;
      const isTrash = info.is_trash;
      this.$refs.contextMenu.show(clientX, clientY, isTrash);
      this.selectFileId = info.id;
    },
    onSelect() {
      console.log('Handle select action');
      this.doBySort(3, { id: this.selectFileId });
    },
    onMove() {
      console.log('Handle move action');
      if (this.isFileSelect && this.selectIds.length > 0) {
        this.doMoveTag(this.selectIds);
      } else {
        this.doMoveTag([this.selectFileId]);
      }
    },
    onDelete() {
      console.log('Handle delete action');
      console.log(this.selectIds, 'selectIds');
      if (this.isFileSelect && this.selectIds.length > 0) {
        this.moveToTrash(this.selectIds, true);
      } else {
        this.moveToTrash([this.selectFileId], true);
      }
    },
    onRename() {
      if (this.selectFileId) {
        this.showEditName = true;
      }
    },
    onTrashRestore() {
      if (this.selectFileId) {
        this.recoverFile(true, this.selectFileId);
      }
    },
    onTrashDelete() {
      // emptyFile
      if (this.selectFileId) {
        this.emptyFile(true, this.selectFileId);
      }
    },
    handleRenameSave(newName) {
      this.showEditName = false;
      // 做列表回显，使用some防止后面的数据也会被处理
      this.infoList.some((item) => {
        if (item.id === this.selectFileId) {
          item.filename = newName;
          return true;
        }
      });
    },
    hideContextMenu(event) {
      if (this.$refs.contextMenu && !event.target.closest('.context-menu')) {
        this.$refs.contextMenu.hide();
      }
    },
    parseURL() {
      parseHash('integration', () => {
        this.toCenter(1);
      });
      parseHash('file_detail', () => {
        let dataStr = getQueryParam(window.location.href, 'data');
        let file = JSON.parse(dataStr);
        this.pageType = '';

        this.selectDetail(file);
        this.eventType = 'file_detail' + new Date().getTime();
        localStorage.setItem('fileDetail', dataStr);
      });
    },
    // 带问题打开全局askai
    goGlobalAskWithQuestion(val = '') {
      this.ask = {
        globalQuestion: val,
        newAsk: true,
      };
      this.goGlobalAsk();
    },
    // 监听快捷键
    handleKeyDown(event) {
      let globalaskCallback = () => {
        this.goGlobalAskWithQuestion();
      };
      let defaultMethod = () => {
        /* 快捷键打开全局askai
         * macOS：⌘ /
         * Windows：Ctrl /
         */
        if (event.key === '/' && event.ctrlKey) {
          globalaskCallback();
        }
      };
      switch (this.operatingSystem) {
        case 'macOS':
          if (event.key === '/' && event.metaKey) {
            globalaskCallback();
          }
          break;
        case 'windows':
          defaultMethod();
          break;
        case 'Linux':
          defaultMethod();
          break;
        default:
          defaultMethod();
          break;
      }
    },
    onPPCDialog() {
      this.showPPCDialog = !this.showPPCDialog;
    },
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hideContextMenu);
  },
};
</script>
<style>
.temp-right-icons {
  position: fixed;
  right: 48px;
  top: 14px;
  z-index: 999; /*不能小于9999，否则会出现share里面的弹框的遮罩在内容上面*/
  &.free {
    .free-btn {
      display: flex;
    }
    top: 9px;
  }
}

.drag-preview {
  position: absolute;
  top: -100px;
  color: #475467;
  font-size: 12px;
  pointer-events: none; /* 防止干扰拖动 */
  display: inline-flex;
  padding: 6px 12px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 6px;
  border: 1px solid #e4e7ec;
  background: #fcfcfd;
  box-shadow:
    0px 4px 8px -2px rgba(0, 0, 0, 0.08),
    0px 2px 4px -2px rgba(0, 0, 0, 0.04);
}
.drag-preview-hidden {
  display: none;
}
.drag-preview-show {
  display: block;
}
.custom-menu {
  position: fixed;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 8px;
  z-index: 1000;
}

.custom-menu ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.custom-menu li {
  padding: 4px 8px;
  cursor: pointer;
}

.custom-menu li:hover {
  background-color: #f0f0f0;
}
</style>
