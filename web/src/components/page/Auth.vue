<template>
  <div :class="`auth-wrapper auth-${pageType}`">
    <div class="header">
      <div class="header-logo" @click="goHome">
        <img class="logo" :src="getImagePath('plaud_logo_text')" alt="logo" />
      </div>
    </div>
    <div class="main">
      <div class="colorful left"></div>
      <div class="access">
        <div class="logo" v-if="logo">
          <img class="zapier" :src="getImagePath(logo)" alt="zapier logo" />
          <img class="arrows" :src="getImagePath('icon_exchange_arrow')" alt="arrows icon" />
          <img class="plaud" :src="getImagePath('plaud_logo_image')" alt="plaud logo" />
        </div>
        <div class="title">
          <div class="detail" v-html="getTranslateStr('auth_title', '%s', pageName)"></div>
        </div>
        <ul class="content">
          <li class="item" v-for="item in authData" :key="item">
            <img class="check" :src="getImagePath('checkmark')" alt="zapier logo" />
            <div class="text" v-html="item"></div>
          </li>
        </ul>
        <div class="commonBtn allow-btn" @click="handleAuth">
          <i v-show="isLoading" class="el-icon-loading mr6 lh20"></i>
          {{ $t('auth_allow_text') }}
        </div>
        <div class="footer">
          {{ getTranslateStr('auth_log_text', '%s', userInfo?.nickname || '') }}
        </div>
      </div>
      <div class="colorful right"></div>
    </div>
  </div>
</template>
<script>
import { getAllQueryParams, getBaseUrl } from '@/util/common';
import { getAuthURLs } from '@/apis/auth';

export default {
  components: {},
  props: {
    pageType: {
      type: String,
      default: '',
    },
    logo: {
      type: String,
      default: '',
    },
    pageName: {
      type: String,
      default: '',
    },
    authTexts: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      userInfo: {},
      authURLs: {},
      isLoading: false,
      timer: null,
    };
  },
  computed: {
    authDataLastIndex() {
      return this.authData.length - 1;
    },
    authData() {
      return this.authTexts;
    },
  },
  created() {
    this.init();
  },
  mounted() {},
  beforeDestroy() {
    this.timer && clearTimeout(this.timer);
    this.isLoading = false;
  },
  methods: {
    init() {
      this.getUserInfo();
      this.getApi();
    },
    getImagePath(icon) {
      try {
        return require(`../../../public/static/${icon}.png`);
      } catch (e) {
        console.error(`Image not found: ../../../public/static/${icon}.png`, e);
        return '';
      }
    },
    isMatchedSSO(email) {
      const regex = /^(google-|apple-).*@plaud\.ai$/;
      return regex.test(email);
    },
    getUserInfo() {
      return this.reqGetInfo('/user/me').then((data) => {
        this.$root.userInfo = this.userInfo = data.data_user;
        // console.log('this.userInfo:', this.userInfo);
        this.$root.userStateInfo = this.userStateInfo = data.data_state;
        let email = this.userInfo?.email || '';
        // let email = '<EMAIL>';
        // let email = '<EMAIL>';
        let emaiSSOFlag = this.isMatchedSSO(email);
        let emailText = this.getTranslateStr('auth_message_three', '%s', `(<u>${email}</u>)`);
        let noemailText = this.$t('auth_message_four');
        let info = !emaiSSOFlag ? emailText : noemailText;
        this.authData[this.authDataLastIndex] = info;
      });
    },
    getApi() {
      this.authURLs = getAuthURLs(this.pageType);
      // console.log('this.authURLs:', this.authURLs);
    },
    goHome() {
      window.location.href = getBaseUrl(window.location.href) || 'https://app.plaud.ai/';
    },
    // 授权
    async handleAuth() {
      this.isLoading = true;
      let params = getAllQueryParams(window.location.href);
      params?.flag && delete params.flag;
      try {
        const queryString = new URLSearchParams(params).toString();
        const data = await this.reqGetInfo(`${this.authURLs.authorize}?${queryString}`);
        if (data?.status === 0) {
          this.setPlaudLogEvent('zapier', { action: 'connected' });
          if (data?.redirect_uri) {
            window.location.href = data?.redirect_uri;
          }
        }

        this.timer = window.setTimeout(() => {
          this.isLoading = false;
        }, 60 * 1000);
      } catch (error) {
        this.isLoading = false;
        console.log('auth failed', error);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.auth-wrapper {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: #1f1f1f;
}
.auth-workspace {
  .main {
    .title {
      .detail {
        letter-spacing: normal;
      }
    }
  }
}
.header {
  width: 100%;
  height: 48px;
  z-index: 2;
  background: #1f1f1f;
  .header-logo {
    height: 100%;
    width: 180px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .logo {
    margin-left: 24px;
    width: 153px;
    height: 20px;
  }
}
.main {
  position: relative;
  z-index: 1;
  flex: 1;
  .access {
    margin: 6% auto;
    width: 440px;
    padding: 80px 48px 40px;
    border-radius: 20px;
    background: #fff;
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
  }
  .logo {
    padding: 0 68px;
    margin-bottom: 24px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    .zapier,
    .plaud {
      width: 64px;
      height: 64px;
      border-radius: 14px;
      background: #fff;
      /* 浮层 */
      box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
    }
    .plaud {
    }
    .arrows {
      width: 32px;
      height: 32px;
      margin: 0 24px;
    }
  }
  .title {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    color: #1f1f1f;
    font-size: 24px;
    font-weight: 400;
    line-height: 32px;
    text-align: center;
    .detail {
      letter-spacing: -1px;
    }
  }
  .content {
    min-height: 100px;
    margin-bottom: 40px;
    font-size: 14px;
    letter-spacing: -0.3px;
    .item {
      display: flex;
      flex-direction: row;
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .check {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-top: 2px;
    }
  }
  .allow-btn {
    margin-bottom: 24px;
  }
  .footer {
    color: #858c9b;
    font-size: 12px;
    line-height: 18px;
  }
  .colorful {
    z-index: -1;
    position: absolute;
    width: 900px;
    height: 471px;
    border-radius: 900px;
  }
  .left {
    left: -8%;
    bottom: -10%;
    opacity: 0.3;
    background: linear-gradient(90deg, #949fff 0%, #ff7cbb 100%);
    filter: blur(100px);
  }
  .right {
    right: -8%;
    top: -10%;
    background: linear-gradient(80deg, #eee4ff 11.14%, #e1f4ff 101.08%);
    filter: blur(75px);
  }
}
@media (min-width: 1900px) {
  .main {
    .access {
      margin: 7% auto;
    }
    .colorful {
      width: 1100px;
      height: 550px;
    }
  }
}
</style>
