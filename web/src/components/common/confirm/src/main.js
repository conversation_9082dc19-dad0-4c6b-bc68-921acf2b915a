import main from './Confirm.vue';

import Vue from 'vue';
let ConfirmConstructor = Vue.extend(main);
let instance;
const Confirm = function (options) {
  options = options || {};
  if (typeof options === 'string') {
    options = {
      msg: options,
    };
  }
  if (!options.cancelname) {
    options.cancelname = this.$t('Filelist_selected_rename_button_left');
  }
  if (!options.okname) {
    options.okname = this.$t('Membership_restore_no_button');
  }
  instance = new ConfirmConstructor({
    data: options,
  });
  instance.$mount();
  document.body.appendChild(instance.$el);
};
export default Confirm;
