import main from './Confirm.vue';
import { createApp } from 'vue';

let instance;
const Confirm = function (options) {
  options = options || {};
  if (typeof options === 'string') {
    options = {
      msg: options,
    };
  }
  if (!options.cancelname) {
    options.cancelname = 'Cancel'; // Default fallback
  }
  if (!options.okname) {
    options.okname = 'OK'; // Default fallback
  }

  // Create Vue 3 app instance
  const app = createApp(main, options);
  const container = document.createElement('div');
  document.body.appendChild(container);
  instance = app.mount(container);
};
export default Confirm;
