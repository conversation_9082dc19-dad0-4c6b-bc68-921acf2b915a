<template>
  <div class="alpSmallLoad" v-if="status">
    <div class="spinner" style="width: 100px; height: 100px" ref="lavContainer"></div>
  </div>
</template>

<script>
import lottie from 'lottie-web';
export default {
  name: 'Loading',
  props: {
    status: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    status(n) {
      if (n) {
        this.$nextTick(() => {
          this.init();
        });
      }
    },
  },
    mounted(){
      if(this.status){
          this.$nextTick(() => {
              this.init();
          });
      }
    },
  methods: {
    init() {
      this.anim = lottie.loadAnimation({
        container: this.$refs.lavContainer,
        renderer: 'svg',
        loop: true,
        autoplay: true,
        animationData: require(`../../data/loading-all.json`),
        rendererSettings: {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
$bgcolor: #1f1f1f;
.alpSmallLoad {
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 99999;
  background-color: transparent;
  top: 0px;
  left: 0px;
  &.fixed {
    position: fixed;
  }

  .spinner {
    position: absolute;
    top: 50%;
    height: 50px;
    left: 50%;
    z-index: 1;
    margin: 0;
  }
}

.spinner {
  margin: 100px auto;
  text-align: center;
  position: fixed;
  z-index: 999999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}
</style>
