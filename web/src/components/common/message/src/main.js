import main from './Message.vue';

import Vue from 'vue';
let MessageConstructor = Vue.extend(main);
let instance;
let instances = [];

const Message = function (options) {
  Message.close();

  options = options || {};
  if (typeof options === 'string') {
    options = {
      message: options,
      type: 'info',
    };
  }
  options.onClose = function () {
    Message.close();
  };
  instance = new MessageConstructor({
    data: options,
  });
  instance.$mount();
  document.body.appendChild(instance.$el);
  instances.push(instance);
};
Message.close = function () {
  if (instances.length < 1) {
    return false;
  }
  for (let i = 0; i < instances.length; i++) {
    instances[i].destroyEL();
  }
  instances = [];
};
export default Message;
