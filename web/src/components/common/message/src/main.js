import main from './Message.vue';
import { createApp } from 'vue';

let instance;
let instances = [];

const Message = function (options) {
  Message.close();

  options = options || {};
  if (typeof options === 'string') {
    options = {
      message: options,
      type: 'info',
    };
  }
  options.onClose = function () {
    Message.close();
  };

  // Create Vue 3 app instance
  const app = createApp(main, options);
  const container = document.createElement('div');
  document.body.appendChild(container);
  instance = app.mount(container);
  instances.push({ app, container, instance });
};

Message.close = function () {
  if (instances.length < 1) {
    return false;
  }
  for (let i = 0; i < instances.length; i++) {
    const { app, container, instance } = instances[i];
    if (instance && instance.destroyEL) {
      instance.destroyEL();
    }
    app.unmount();
    if (container.parentNode) {
      container.parentNode.removeChild(container);
    }
  }
  instances = [];
};
export default Message;
