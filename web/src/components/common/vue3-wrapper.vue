<script>
import Vue from 'vue';
export default Vue.extend({
  name: 'Vue3<PERSON>rapper',
  props: [
    'data',
    'onChange',
    'onReady',
    'generateStatus',
    'eventType',
    'userInfo',
    'allFiles',
    'userBaseInfo',
    'pageType',
    'pageParams',
    'myFiles',
    'leftSmallStatus',
  ],
  data() {
    return {
      vue3Instance: null,
      vue3: null,
      // options: null,
      // data: null,
    };
  },
  created() {
    this.loadVue3Component();
  },
  watch: {
    // defaultValue(val) {
    //   console.log('beforeUpdate', 'defaultValue', this.options);
    //   if (this.options) {
    //     this.options.value.defaultValue = val;
    //   }
    // },
    data(val) {
      console.log('beforeUpdate', 'data', this.options);
      if (this.options) {
        this.options.value.data = val;
      }
    },
    'data.generateCode'(val) {
      console.log('beforeUpdate', 'data', this.options, this.data, val);
      if (this.options) {
        this.options.value.data = { ...this.data };
      }
    },

    generateStatus(val) {
      if (this.options) {
        this.options.value.generateStatus = val;
      }
    },
    leftSmallStatus(val) {
      if (this.options) {
        this.options.value.leftSmallStatus = val;
      }
    },

    // generateCode(val) {
    //   if (this.options) {
    //     this.options.value.generateCode = val;
    //   }
    // },

    eventType(val) {
      console.log('beforeUpdate', 'data', this.options);
      if (this.options) {
        this.options.value.eventType = val;
      }
    },
    userInfo(val) {
      //实际是user_state信息
      if (this.options) {
        this.options.value.userInfo = val;
      }
    },
    userBaseInfo(val) {
      if (this.options) {
        this.options.value.userBaseInfo = val;
      }
    },
    allFiles(val) {
      if (this.options) {
        this.options.value.allFiles = val;
      }
    },
    myFiles(val) {
      if (this.options) {
        this.options.value.myFiles = val;
      }
    },
    pageType(val) {
      if (this.options) {
        this.options.value.pageType = val;
      }
    },
    pageParams(val) {
      if (this.options) {
        this.options.value.pageParams = val;
      }
    },
  },
  updated() {
    console.log('beforeUpdate', 'updated');
  },
  beforeUpdate() {
    console.log('beforeUpdate', 'beforeUpdate');
  },

  beforeDestroy() {
    console.log('beforeDestroy');
    if (this.vue3Instance) {
      this.vue3Instance?.unmount?.();
      this.vue3Instance = null;
    }
  },
  methods: {
    async loadVue3Component() {
      const result = await Promise.all([
        import(`editor/vue`),
        import(`editor/app`),
        import(`editor/svg-icon`),
      ]);
      this.vue3 = result[0];

      const Vue3Component = result[1];
      const SvgIcon = result[2];

      this.options = this.vue3.ref({
        data: this.data,
        onChange: this.onChange,
        onReady: this.onReady,
        generateStatus: this.generateStatus,
        leftSmallStatus: this.leftSmallStatus,
        // generateCode: this.generateCode,
        eventType: this.eventType,
        userInfo: this.userInfo,
        userBaseInfo: this.userBaseInfo,
        allFiles: this.allFiles,
        myFiles: this.myFiles,
        pageType: this.pageType,
        pageParams: this.pageParams,
        // defaultValue: this.defaultValue,
        // name: this.name,
        // onNameChange: this.onNameChange,
      });
      console.log('beforeUpdate111', this.options.value);

      this.vue3Instance = this.vue3.createApp({
        render: () => this.vue3.h(Vue3Component.default, this.options.value),
      });
      this.vue3Instance.component('SvgIcon', SvgIcon.default);
      this.vue3Instance.mount(this.$el);
    },
  },
  render(h) {
    return h('div', { class: 'h-full' });
  },
});
</script>
