<template>
  <div class="iptFlex" @mouseover="readonlyStatus = false">
    <input
      :data-pld="dataPld || 'common-input'"
      :type="myType"
      v-model.lazy="myValue"
      @input="changeIpt"
      @keydown.enter="enterEvt"
      @blur="handleInputBlur"
      :placeholder="placeholder"
      :readonly="readonlyStatus"
      autocomplete="off"
    />
    <div
      class="iconfont"
      :data-pld="isopen ? 'common-hidedata-icon' : 'common-showdata-icon'"
      :class="[isopen ? 'icon-eyes-open' : 'icon-eyes-closed']"
      @click="isopen = !isopen"
      v-if="type == 'password'"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'MyIpt',
  props: {
    type: {
      type: String,
      default: 'text',
    },
    value: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    clearError: {
      type: Boolean,
      default: false,
    },
    from: {
      type: String,
      default: '',
    },
    dataPld: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isopen: false,
      myValue: this.value,
      readonlyStatus: true, //为了处理自动填充
      hasInputChanged: false, // 标志位，是否输入过
    };
  },
  computed: {
    myType() {
      if (this.type == 'password') {
        return this.isopen ? 'text' : 'password';
      }
      return this.type;
    },
  },
  watch: {
    value() {
      this.myValue = this.value;
    },
    myValue: {
      handler(n, o) {
        this.$emit('input', n);
      },
      deep: true,
    },
  },
  methods: {
    changeIpt() {
      if (this.hasInputChanged) {
        return;
      }
      this.hasInputChanged = true;
      if (this.clearError && this.$parent.formError) {
        this.$parent.formError = {};
      }
    },
    handleInputBlur() {
      this.hasInputChanged = false; // 失焦重置输入标志位
      this.$emit('InputBlur');
    },
    enterEvt(event) {
      // 解决因为.lazy问题导致的回车时数据不实时问题
      this.$emit('input', event.target.value);
      this.$emit('doenter');
    },
  },
};
</script>

<style lang="scss" scoped>
.iptFlex {
  height: 40px;
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 15px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: #f2f4f7;
  transition: all 0.2s ease-in-out;
  &:hover {
    background-color: #e4e7ec;
  }
  input {
    outline: none;
    height: 100%;
    flex: 1;
    border: none;
    background-color: transparent;
    font-size: 15px;
    &::placeholder {
      font-family:
        'SF Pro SC', 'SF Pro Text', 'SF Pro Icons', 'PingFang SC', 'Helvetica Neue', Helvetica,
        Arial, sans-serif;
      font-size: 14px;
      font-weight: 400;
      letter-spacing: -0.3px;
      color: rgba(152, 162, 179, 0.7);
    }
  }
  .iconfont {
    color: rgba(152, 162, 179, 0.7);
    font-size: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
  &:focus,
  &.active {
    .iconfont {
      color: #1f1f1f;
    }
  }
}
</style>
