<template>
  <div ref="lavContainer" v-if="type == 'loading'"></div>
  <Loading :status="true" v-else-if="finshed"/>
  <div class="amLoading flex-center" style="flex-direction: column" v-else-if="!finshed">
    <div style="width: 90px; height: 100px" ref="lavContainer"></div>
    <div class="amLetter">
      <span v-if="type == 'trans'"
        >{{ $t('Filedetail_status_trancribing_title') }}<br />{{
          $t('Filedetail_status_trancribing_tips')
        }}</span
      >
      <span v-else-if="type == 'summary'">
        <template v-if="summaryType === 1">
          {{ $t('Filedetail_status_summarizing_title') }}<br />{{
            $t('Filedetail_status_summarizing_tips')
          }}
        </template>
        <template v-else>
          {{ $t('Filedetail_status_nosummary') }}
        </template>
      </span>
    </div>
  </div>
</template>
<script>
import lottie from 'lottie-web';
export default {
  props: {
    type: {
      type: String,
      default: 'trans',
    },
      finshed:{ //转写是否已完成的状态
        type:Boolean,
          default:false
      },
    summaryType: {
      //2是转写未完成总结还没开始
      type: [Number, String],
      default: 1,
    },
  },
  data() {
    return {
      anim: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
        if(!this.finshed){
            this.init();
        }
    });
  },
  beforeUnmount() {
    this.anim.destroy();
  },
  methods: {
    init() {
      this.anim = lottie.loadAnimation({
        container: this.$refs.lavContainer,
        renderer: 'canvas',
        loop: true,
        autoplay: true,
        animationData: require(`@/data/${this.type}.json`),
        rendererSettings: {},
      });
    },
  },
};
</script>
<style scoped lang="scss">
.amLoading {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 99;
  background-color: #fff;
}
.amLetter {
  font-weight: 400;
  font-size: 16px;
  color: rgba(31, 31, 31, 0.8);
  margin-top: 26px;
  text-align: center;
  padding: 0px 10px;
}
</style>
