<template>
  <div class="iptFlex standard-font" @mouseover="readonlyStatus = false">
    <input
      :type="inputType"
      v-model.lazy="inputValue"
      @input="handleInput"
      @blur="handleBlur"
      @keydown.enter="handleKeydownEnter"
      :placeholder="placeholder"
      :readonly="readonlyStatus"
      autocomplete="off"
    />
    <div
      class="iconfont"
      :class="[isopen ? 'icon-eyes-open' : 'icon-eyes-closed']"
      @click="isopen = !isopen"
      v-if="type == 'password'"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'BindAccountInput',
  props: {
    type: {
      type: String,
      default: 'text',
    },
    value: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    clearError: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isopen: false,
      inputValue: this.value,
      readonlyStatus: true, //为了处理自动填充
      hasInputChanged: false, // 标志位，是否输入过
    };
  },
  computed: {
    inputType() {
      if (this.type == 'password') {
        return this.isopen ? 'text' : 'password';
      }
      return this.type;
    },
  },
  watch: {
    value() {
      this.inputValue = this.value;
    },
    inputValue: {
      handler(n, o) {
        this.$emit('input', n);
      },
      deep: true,
    },
  },
  methods: {
    handleInput() {
      if (!this.hasInputChanged) {
        this.hasInputChanged = true;
        if (this.clearError && this.$parent.formError) {
          this.$parent.formError = {};
        }
      }
    },
    handleBlur() {
      this.hasInputChanged = false; // 失焦重置输入标志位
      this.$emit('InputBlur');
    },
    handleKeydownEnter() {
      this.$emit('InputKeydowEnter');
    },
  },
};
</script>

<style lang="scss" scoped>
.iptFlex {
  height: 40px;
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 15px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: #F2F4F7;
  &:hover {
    background-color: #E4E7EC;
  }
  input {
    outline: none;
    height: 100%;
    flex: 1;
    border: none;
    background-color: transparent;
    font-size: 15px;
    &::placeholder {
      font-size: 14px;
      font-weight: 400;
      letter-spacing: -0.3px;
      color: rgba(152, 162, 179, 0.7);
    }
  }
  .iconfont {
    margin-left: 6px;
    color: rgba(152, 162, 179, 0.7);
    font-size: 18px;
    cursor: pointer;
  }
  &:focus,
  &.active {
    .iconfont {
      color: #1f1f1f;
    }
  }
}
</style>
