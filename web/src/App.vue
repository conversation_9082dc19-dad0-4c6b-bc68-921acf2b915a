<template>
  <MainBody></MainBody>
</template>

<script>
import MainBody from '@/components/MainBody';
import { changeZELang } from '@/util/zendesk';
export default {
  components: { MainBody },
  created() {
    this.initLang();
  },
  methods: {
    initLang() {
      let webLang = window.localStorage.getItem('pweblang') || this.$i18n.locale || 'en_US';
      document.documentElement.setAttribute('lang', webLang);
    },
  },
};
</script>
