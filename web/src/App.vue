<template>
  <MainBody></MainBody>
</template>

<script>
import MainBody from '@/components/MainBody.vue';
import { changeZELang } from '@/util/zendesk';
import { useI18n } from 'vue-i18n';

export default {
  name: 'App',
  components: { MainBody },
  setup() {
    const { locale } = useI18n();

    const initLang = () => {
      let webLang = window.localStorage.getItem('pweblang') || locale.value || 'en_US';
      document.documentElement.setAttribute('lang', webLang);
    };

    return {
      initLang
    };
  },
  created() {
    this.initLang();
  }
};
</script>
