import { createRouter, createWebHistory } from 'vue-router';

let myRoutes = [
  /*默认访问*/
  {
    path: '/',
    name: 'index',
    component: () => import('../components/page/commonapp/Index.vue'),
  },
  /*自定义模板帮助页面*/
  {
    path: '/templates_help',
    name: 'templates_help',
    component: () => import('../components/page/commonapp/TemplatesHelp.vue'),
  },
];

export default createRouter({
  history: createWebHistory(),
  routes: myRoutes,
});
