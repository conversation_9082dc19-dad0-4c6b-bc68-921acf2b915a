import { createRouter, createWebHistory } from 'vue-router';

// Convert to dynamic imports for Vue 3
const Index = () => import('../components/MainBody.vue');
const Subscribe = () => import('../components/page/Subscribe.vue');

export default createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'index',
      component: Index,
    },
    {
      path: '/index',
      name: 'index',
      component: Index,
    },
    {
      path: '/subscribe',
      name: 'subscribe',
      component: Subscribe,
    },
  ],
});
