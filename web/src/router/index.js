import Vue from 'vue';
import Router from 'vue-router';
Vue.use(Router);

const Index = (r) => require.ensure([], () => r(require('../components/MainBody')), 'home');
const Subscribe = (r) =>
  require.ensure([], () => r(require('../components/page/Subscribe')), 'home');

export default new Router({
  routes: [
    {
      path: '/',
      name: 'index',
      component: Index,
    },
    {
      path: '/index',
      name: 'index',
      component: Index,
    },
    {
      path: '/subscribe',
      name: 'subscribe',
      component: Subscribe,
    },
  ],
});
