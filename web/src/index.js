import './common/common';
import './common/date-utils';
import './common/dates';
// localStorage.setItem(
//   'tokenstr',
//   'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0MzIyMDZiYjJhYTQzYjU3MDZjYWFmYjhkZjQwM2EwNSIsImF1ZCI6IiIsImV4cCI6MTc0OTExMTE4MywiaWF0IjoxNzIzMTkxMTgzLCJjbGllbnRfaWQiOiJ3ZWIifQ.T_1k4YEzqkh97npxOF_f0IsbcYfl_I_FriJ2aXrwiTE',
// );
import * as Sentry from '@sentry/vue';
import { createApp } from 'vue';
import App from './App.vue';
import store from './store';
import router from './router';

// import SvgIcon from './components/common/svg-icon/index.vue';
// Vue.component('SvgIcon', SvgIcon);

Sentry.init({
  environment: location.hostname.indexOf('plaud.ai') !== -1 ? 'production' : 'test',
  Vue,
  dsn: 'https://<EMAIL>/3',
  integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
  // Tracing
  tracesSampleRate: 1, //  Capture 100% of the transactions
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  // tracePropagationTargets: ['localhost', /^https:\/\/yourserver\.io\/api/],
  // Session Replay
  replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 0.1, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

import RequestMixins from './util/RequestMixins';
import CommonMixins from '@/util/CommonMixins';
import FirebaseMixins from '@/util/FirebaseMixins';
import Message from './components/common/message';
import Confirm from './components/common/confirm';

import VueClipboard from 'vue-clipboard3';

import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import './styles/element-variables.scss';

// import mavonEditor from 'mavon-editor';
// import 'mavon-editor/dist/css/index.css';
// Vue.use(mavonEditor);

import { registerDirectives } from './util/Directive';
import i18n, { setLocale, getCurrentLocale } from './language/index';

import analyticsPlugin from './common/tracker-plugin';

import '@/styles/style.scss';
import '@/styles/index.css';
import '@/styles/material-icons.css';
import 'vue3-perfect-scrollbar/style.css';

// import vuePerfectScrollbar from 'vue-perfect-scrollbar/index';

import { RecycleScroller, DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
import '@/data/material_icons.js';
import { initMaterialIconsDisplay } from '@/util/material-icons-collection';
import emitter from './util/event-bus';
// const baseSize = 100;
// function setRem() {
//   let scale = document.documentElement.clientWidth / 1920;
//   scale = Math.min(scale, 1);
//   // scale = Math.max(scale, 0.6);
//   let mySize = baseSize * scale;
//   document.documentElement.style.fontSize = mySize + 'px';
// }
// window.onresize = function () {
//   // setRem();
// };

// Initialize app asynchronously
async function initApp() {
  const locale = getCurrentLocale();
  await setLocale(locale);

  // Create Vue 3 app
  const app = createApp(App);

  // Configure app
  app.use(store);
  app.use(i18n);
  app.use(router);

  // Register directives
  registerDirectives(app);

  // Use Element Plus
  app.use(ElementPlus);

  // Global properties (replacing Vue.prototype)
  app.config.globalProperties.setMessage = Message;
  app.config.globalProperties.setConfirm = Confirm;

  // Global components
  app.component('RecycleScroller', RecycleScroller);
  app.component('DynamicScroller', DynamicScroller);
  app.component('DynamicScrollerItem', DynamicScrollerItem);

  // Provide global data (replacing root data)
  app.provide('BUS', emitter);
  app.provide('userInfo', {});
  app.provide('userStateInfo', {});
  app.provide('myAnalytics', null);

  // Make event bus globally available
  app.config.globalProperties.$bus = emitter;

  // Mount app
  app.mount('#app');

  // 初始化 Material Icons 显示控制
  initMaterialIconsDisplay();
}

// Initialize the app
initApp().catch(console.error);
