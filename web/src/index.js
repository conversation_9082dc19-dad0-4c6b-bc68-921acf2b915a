import './common/common';
import './common/date-utils';
import './common/dates';
// localStorage.setItem(
//   'tokenstr',
//   'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0MzIyMDZiYjJhYTQzYjU3MDZjYWFmYjhkZjQwM2EwNSIsImF1ZCI6IiIsImV4cCI6MTc0OTExMTE4MywiaWF0IjoxNzIzMTkxMTgzLCJjbGllbnRfaWQiOiJ3ZWIifQ.T_1k4YEzqkh97npxOF_f0IsbcYfl_I_FriJ2aXrwiTE',
// );
import * as Sentry from '@sentry/vue';
import Vue from 'vue';
import App from './App';
import store from './store';

// import SvgIcon from './components/common/svg-icon/index.vue';
// Vue.component('SvgIcon', SvgIcon);

Sentry.init({
  environment: location.hostname.indexOf('plaud.ai') !== -1 ? 'production' : 'test',
  Vue,
  dsn: 'https://<EMAIL>/3',
  integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
  // Tracing
  tracesSampleRate: 1, //  Capture 100% of the transactions
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  // tracePropagationTargets: ['localhost', /^https:\/\/yourserver\.io\/api/],
  // Session Replay
  replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 0.1, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

import RequestMixins from './util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from '@/util/CommonMixins';
Vue.mixin(CommonMixins);

import FirebaseMixins from '@/util/FirebaseMixins';
Vue.mixin(FirebaseMixins);

import Message from './components/common/message';
Vue.prototype.setMessage = Message;
import Confirm from './components/common/confirm';
Vue.prototype.setConfirm = Confirm;

import VueClipBoard from 'vue-clipboard2';
Vue.use(VueClipBoard);

import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './styles/element-variables.scss';
Vue.use(ElementUI);

// import mavonEditor from 'mavon-editor';
// import 'mavon-editor/dist/css/index.css';
// Vue.use(mavonEditor);

import './util/Directive';
import i18n, { setLocale, getCurrentLocale } from './language/index';
Vue.config.productionTip = false;

import analyticsPlugin from './common/tracker-plugin';
Vue.use(analyticsPlugin, { trackingId: 'G-5N1K2J3M5R' });

import '@/styles/style.scss';
import '@/styles/index.css';
import '@/styles/material-icons.css';

// import vuePerfectScrollbar from 'vue-perfect-scrollbar/index';

import { RecycleScroller, DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller';
Vue.component('RecycleScroller', RecycleScroller);
Vue.component('DynamicScroller', DynamicScroller);
Vue.component('DynamicScrollerItem', DynamicScrollerItem);
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
import '@/data/material_icons.js';
import { initMaterialIconsDisplay } from '@/util/material-icons-collection';
// const baseSize = 100;
// function setRem() {
//   let scale = document.documentElement.clientWidth / 1920;
//   scale = Math.min(scale, 1);
//   // scale = Math.max(scale, 0.6);
//   let mySize = baseSize * scale;
//   document.documentElement.style.fontSize = mySize + 'px';
// }
// window.onresize = function () {
//   // setRem();
// };

const locale = getCurrentLocale();
await setLocale(locale);

new Vue({
  el: '#app',
  store,
  i18n,
  template: '<App/>',
  components: { App },
  data: function () {
    return {
      BUS: new Vue(),
      userInfo: {},
      userStateInfo: {},
      myAnalytics: null,
    };
  },
  // mounted() {
  //   // setRem();
  // },
});

// 初始化 Material Icons 显示控制
initMaterialIconsDisplay();
