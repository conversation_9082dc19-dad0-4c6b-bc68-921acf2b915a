import Vue from 'vue';
import VueI18n from 'vue-i18n';
// import enMessages from './en_US.json';

Vue.use(VueI18n);

// 支持的语言列表
const supportedLocales = [
  'en_US',
  'es_ES',
  'fr_FR',
  'de_DE',
  'it_IT',
  'ja_JP',
  'ko_KR',
  'pt_PT',
  'zh_CN',
  'zh_TW',
];

// 浏览器语言映射表
const languageMap = {
  en: 'en_US',
  es: 'es_ES',
  fr: 'fr_FR',
  de: 'de_DE',
  it: 'it_IT',
  ja: 'ja_JP',
  ko: 'ko_KR',
  pt: 'pt_PT',
  zh: 'zh_CN',
  'zh-CN': 'zh_CN',
  'zh-TW': 'zh_TW',
  'zh-HK': 'zh_TW',
  'en-US': 'en_US',
};

// 获取系统语言
const getBrowserLanguage = () => {
  try {
    const language = navigator.language || navigator.userLanguage;
    console.log('getBrowserLanguage', language);
    return languageMap[language] || 'en_US';
  } catch (error) {
    console.log('getBrowserLanguage', error);
    return 'en_US';
  }
};

// 获取当前语言
export const getCurrentLocale = () => {
  return (
    sessionStorage.getItem('comlang') ||
    window.localStorage.getItem('pweblang') ||
    getBrowserLanguage() ||
    'en_US'
  );
};

// 动态加载语言包
const loadLocaleMessages = async (locale) => {
  try {
    const messages = await import(`./${locale}.json`);
    return messages.default;
  } catch (error) {
    console.error(`Failed to load locale: ${locale}`, error);
    return {};
  }
};

// 创建 i18n 实例
const i18n = new VueI18n({
  locale: getCurrentLocale(),
  fallbackLocale: 'en_US',
  messages: {
    // en_US: enMessages,
  },
});

// 设置语言
export const setLocale = async (locale) => {
  if (!supportedLocales.includes(locale)) {
    // console.warn(`Unsupported locale: ${locale}`);
    // return;
    locale = 'en_US';
  }

  try {
    // 加载语言包
    const messages = await loadLocaleMessages(locale);

    // 设置语言
    i18n.locale = locale;
    i18n.setLocaleMessage(locale, messages);

    // 保存语言设置
    sessionStorage.setItem('comlang', locale);
    window.localStorage.setItem('pweblang', locale);

    // 设置 HTML lang 属性
    document.documentElement.setAttribute('lang', locale);
  } catch (error) {
    console.error(`Failed to set locale: ${locale}`, error);
  }
};

// 初始化加载默认语言
// setLocale(getCurrentLocale());

export default i18n;
