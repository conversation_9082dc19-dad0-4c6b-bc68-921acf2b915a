import { ref } from 'vue';
import { tags } from '../data/TagStyle';
import { useRequest } from './useRequest';

export function useTag() {
  const { reqPostInfo, reqPatchInfo, reqDeleteInfo, reqGetInfo } = useRequest();
  
  // Reactive data
  const tagStyleList = ref(tags);

  // Methods
  const getTagStyle = (info, isSet = false) => {
    let style = {};
    if (isSet) {
      style['background-color'] = info.fontColor;
      style['border-color'] = info.bgColor;
    } else {
      style['background-color'] = info.bgColor;
      style['color'] = info.fontColor;
    }
    return style;
  };

  const getActiveTag = (color) => {
    return tagStyleList.value.find((item) => item.fontColor == color);
  };

  const getCTagStyle = (color) => {
    let tagInfo = getActiveTag(color);
    if (tagInfo) {
      return getTagStyle(tagInfo);
    }
    return {};
  };

  const createTagInfo = (info) => {
    return reqPostInfo('/filetag/', info);
  };

  const updateTagInfo = (info) => {
    return reqPatchInfo('/filetag/' + info.id, info);
  };

  const deleteTagInfo = (id) => {
    return reqDeleteInfo('/filetag/' + id);
  };

  const getTagInfoList = () => {
    return reqGetInfo('/filetag/');
  };

  const updateFilesTag = (fildIds, tagId) => {
    return reqPostInfo('/file/update-tags', { file_id_list: fildIds, filetag_id: tagId });
  };

  return {
    // Data
    tagStyleList,
    
    // Methods
    getTagStyle,
    getActiveTag,
    getCTagStyle,
    createTagInfo,
    updateTagInfo,
    deleteTagInfo,
    getTagInfoList,
    updateFilesTag,
  };
}
