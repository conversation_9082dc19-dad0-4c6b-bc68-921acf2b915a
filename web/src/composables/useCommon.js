import { ref, computed } from 'vue';
import { useTag } from './useTag';

// Date prototype extension (keeping as is since it's a global extension)
Date.prototype.Format1 = function (fmt) {
  //author: meizz 默认yyyy-MM-dd
  if (!fmt) {
    fmt = 'yyyy-MM-dd';
  }
  var o = {
    'M+': this.getMonth() + 1, //月份
    'd+': this.getDate(), //日
    'h+': this.getHours() % 12 == 0 ? 12 : this.getHours() % 12, //小时
    'H+': this.getHours(), //小时
    'm+': this.getMinutes(), //分
    's+': this.getSeconds(), //秒
    'q+': Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
    a: this.getHours() > 12 ? '下午' : '上午',
    EEEEE: '周' + ['日', '一', '二', '三', '四', '五', '六'][this.getDay()],
  };
  if (/(y+)/.test(fmt))
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
  for (var k in o)
    if (new RegExp('(' + k + ')').test(fmt))
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
  fmt = fmt.replace('周', '星期');
  return fmt;
};

export function useCommon() {
  // Include tag functionality
  const tagComposable = useTag();
  
  // Reactive data
  const speakerColors = ref(['#F9A251', '#9692C9', '#70A2EC', '#3DC8C8']);
  const speakerMap = ref({});
  const speakerTrans = ref([]); //speaker对应的trans

  // Computed properties
  const hasSpeaker = computed(() => Object.values(speakerMap.value).length);

  // Methods
  const getTimeStr = (time, hour = false, ism = true, hasMin = false) => {
    //ism是不是毫秒
    if (!time && time !== 0) {
      return '';
    }
    if (ism) {
      time = Math.floor(time / 1000);
    }
    let m = Math.floor(time / 60),
      h = 0; //向下取整
    if (m >= 60) {
      h = Math.floor(m / 60);
      m = m - h * 60;
    }
    let isZero = (num) => {
      return (num < 10 ? '0' : '') + num;
    };
    let s = Math.floor(time - m * 60 - h * 3600); //四舍五入
    let str = isZero(s);
    if (m > 0 || hasMin) {
      str = isZero(m) + ':' + str;
    }
    if (h > 0 || hour) {
      if (!str.includes(':')) {
        str = isZero(m) + ':' + str;
      }
      str = isZero(h) + ':' + str;
    }
    return str;
  };

  const getTimeName = (times, fix = ['h', 'm', 's']) => {
    let arr = times.split(':').map(Number),
      str = [];
    for (let i = arr.length; i--; i >= 0) {
      str.unshift(arr[i] + fix[fix.length - 1 - str.length]);
    }
    return str.join(' ');
  };

  const getNameEllipse = (name, len = 20) => {
    if (!name) return '';
    if (name.length <= len) return name;
    return name.substring(0, len) + '...';
  };

  const setSpeakerColor = (transList, isInit = true) => {
    if (!transList || !transList.length) return;
    
    let speakers = [];
    transList.forEach((item) => {
      if (item.speaker && !speakers.includes(item.speaker)) {
        speakers.push(item.speaker);
      }
    });
    
    if (isInit) {
      speakerMap.value = {};
    }
    
    speakers.forEach((speaker, index) => {
      if (!speakerMap.value[speaker]) {
        speakerMap.value[speaker] = speakerColors.value[index % speakerColors.value.length];
      }
    });
  };

  const getImagePath = (imageName) => {
    return `/static/${imageName}.png`;
  };

  return {
    // Include all tag functionality
    ...tagComposable,
    
    // Data
    speakerColors,
    speakerMap,
    speakerTrans,
    
    // Computed
    hasSpeaker,
    
    // Methods
    getTimeStr,
    getTimeName,
    getNameEllipse,
    setSpeakerColor,
    getImagePath,
  };
}
