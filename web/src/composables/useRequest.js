import { ref, getCurrentInstance } from 'vue';
import HttpService from '../util/HttpService';

export function useRequest() {
  const instance = getCurrentInstance();
  const isLoading = ref(false);

  const requestError = (data) => {
    if (typeof data == 'object' && typeof data.status != 'undefined' && data.status > 200) {
      switch (data.status) {
        case 401: //token过期
          localStorage.removeItem('tokenstr');
          if (
            ['/buydetail', '/transdetail', '/userdetail', '/audiouploadintro'].some((item) =>
              document.location.href.includes(item),
            )
          ) {
            //h5页面不做任何处理
          } else if (
            document.location.href.indexOf('index.html') > -1 ||
            document.location.href.indexOf('.html') == -1
          ) {
            document.location.reload();
          } else {
            document.location.href = 'index.html';
          }
          break;
      }
    } else {
      // this.setMessage(data.msg);
    }
  };

  const showSuccessMsg = (msg) => {
    if (instance?.appContext.config.globalProperties.setMessage) {
      instance.appContext.config.globalProperties.setMessage({ 
        message: msg, 
        autoClose: true, 
        type: 'success' 
      });
    }
  };

  const showErrorMsg = (msg) => {
    if (instance?.appContext.config.globalProperties.setMessage) {
      instance.appContext.config.globalProperties.setMessage({ 
        message: msg, 
        autoClose: false, 
        type: 'error' 
      });
    }
  };

  const showNormalMsg = (msg) => {
    if (instance?.appContext.config.globalProperties.setMessage) {
      return instance.appContext.config.globalProperties.setMessage({ 
        message: msg, 
        autoClose: false 
      });
    }
  };

  const showBackMsg = (msg) => {
    let options = { message: msg, autoClose: false };
    if (instance?.appContext.config.globalProperties.setMessage) {
      instance.appContext.config.globalProperties.setMessage(options);
    }
    return options;
  };

  const reqGetInfo = (url, params = {}) => {
    return new Promise((resolve, reject) => {
      HttpService.get(url, params)
        .then((response) => {
          if (response.data && response.data.code === 200) {
            resolve(response.data.data);
          } else {
            requestError(response.data);
            reject(response.data);
          }
        })
        .catch((error) => {
          requestError(error);
          reject(error);
        });
    });
  };

  const reqPostInfo = (url, data = {}) => {
    return new Promise((resolve, reject) => {
      HttpService.post(url, data)
        .then((response) => {
          if (response.data && response.data.code === 200) {
            resolve(response.data.data);
          } else {
            requestError(response.data);
            reject(response.data);
          }
        })
        .catch((error) => {
          requestError(error);
          reject(error);
        });
    });
  };

  const reqPatchInfo = (url, data = {}) => {
    return new Promise((resolve, reject) => {
      HttpService.patch(url, data)
        .then((response) => {
          if (response.data && response.data.code === 200) {
            resolve(response.data.data);
          } else {
            requestError(response.data);
            reject(response.data);
          }
        })
        .catch((error) => {
          requestError(error);
          reject(error);
        });
    });
  };

  const reqDeleteInfo = (url) => {
    return new Promise((resolve, reject) => {
      HttpService.delete(url)
        .then((response) => {
          if (response.data && response.data.code === 200) {
            resolve(response.data.data);
          } else {
            requestError(response.data);
            reject(response.data);
          }
        })
        .catch((error) => {
          requestError(error);
          reject(error);
        });
    });
  };

  return {
    // Data
    isLoading,
    
    // Methods
    requestError,
    showSuccessMsg,
    showErrorMsg,
    showNormalMsg,
    showBackMsg,
    reqGetInfo,
    reqPostInfo,
    reqPatchInfo,
    reqDeleteInfo,
  };
}
