<script setup>
import {
  ref,
  watch,
  defineAsyncComponent,
  onBeforeUpdate,
  onMounted,
  nextTick,
  computed,
  getCurrentInstance,
  markRaw,
} from 'vue';
import SkeletonPage from './components/skeleton-page/index.vue';
import Member from './components/member/index.vue';
import { Toaster } from 'sonner';
import './assets/styles/index.css';
const Container = defineAsyncComponent(() => import('./container.vue'));
const Chat = defineAsyncComponent(() => import('./components/chat/web-index.vue'));
const FreeTrial = defineAsyncComponent(() => import('./components/member/dialog/FreeTrial.vue'));
const TemplateCommunity = defineAsyncComponent(
  () => import('./components/template-community/index.vue'),
);

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        id: '',
      };
    },
  },
  onChange: {
    type: Function,
  },
  onReady: {
    type: Function,
  },
  generateStatus: {
    type: Number,
  },
  leftSmallStatus: {
    type: Boolean,
    default: false,
  },
  // generateCode: {
  //   type: Number,
  // },

  eventType: {
    type: String,
  },
  userInfo: {
    type: Object,
  },
  userBaseInfo: {
    type: Object,
  },
  // 不包含垃圾箱文件
  allFiles: {
    type: Array,
  },
  // 所有文件
  myFiles: {
    type: Array,
  },
  pageType: {
    type: String,
    default: '',
  },
  pageParams: {
    type: Object,
    default: () => {},
  },
});

let info = computed(() => {
  if (props.userBaseInfo) {
    // console.log('props.userBaseInfo:', props.userBaseInfo)
    let { membership_type } = props.userInfo;
    let { nickname, id, membership_id, globalQuestion, newAsk } = props.userBaseInfo;
    return {
      myFiles: props.myFiles,
      allFiles: props.allFiles,
      userInfo: {
        membershipId: membership_id,
        name: nickname,
        userId: id,
        membership_type,
      },
      question: globalQuestion,
      newAsk,
    };
  } else {
    return {};
  }
});

// let widgetType = computed(() => props.pageType? props.pageType : 'container')
let widgetType = computed(() => props.pageType);

window.isApp = false;

let widget = ref({
  chat: {
    // 全局ask ai
    component: markRaw(Chat),
    propsData: {
      isApp: false,
      info,
      onChange: props.onChange,
    },
    eventHandlers: {},
  },
  free: {
    component: markRaw(FreeTrial),
    propsData: {
      userInfo: props.userInfo,
      userBaseInfo: props.userBaseInfo,
      data: props.data,
      onChange: props.onChange,
    },
  },
  // 模版社区
  templateCommunity: {
    component: markRaw(TemplateCommunity),
    propsData: {
      userInfo: props.userInfo,
      userBaseInfo: props.userBaseInfo,
      data: props.data,
      leftSmallStatus: props.leftSmallStatus,
      onChange: props.onChange,
      pageParams: props.pageParams,
      pageType: props.pageType,
    },
  },
  // container: { // 编辑器
  //   component: Container,
  //   propsData: {
  //     data: props.data,
  //     onChange: props.onChange,
  //     onReady: props.onReady,
  //     generateStatus: props.generateStatus,
  //     eventType: props.eventType,
  //     userInfo: props.userInfo
  //   },
  //   eventHandlers: {}
  // }
});

// 更新 widget 的 props
const updateWidgetProps = (widgetType, propsData) => {
  if (widget.value[widgetType]) {
    widget.value[widgetType].propsData = {
      ...widget.value[widgetType].propsData,
      ...propsData,
    };
  }
};

// 监听所有相关 props 的变化
watch(
  [
    () => props.leftSmallStatus,
    () => props.userInfo,
    () => props.userBaseInfo,
    () => props.data,
    () => props.onChange,
    () => info.value,
    () => props.pageType,
  ],
  ([leftSmallStatus, userInfo, userBaseInfo, data, onChange, infoValue, pageType]) => {
    // 更新 templateCommunity 的 props
    updateWidgetProps('templateCommunity', {
      leftSmallStatus,
      userInfo,
      userBaseInfo,
      data,
      pageType,
      onChange,
    });

    // 更新 free 的 props
    updateWidgetProps('free', {
      userInfo,
      userBaseInfo,
      data,
      onChange,
    });

    // 更新 chat 的 props
    updateWidgetProps('chat', {
      info: infoValue,
      onChange,
    });
  },
  { deep: true, immediate: true },
);

onMounted(() => {
  // console.log('pageType is me:', props.pageType)
  // console.log('pageType is me-props.allFiles:', props.allFiles)
});
</script>

<template>
  <suspense>
    <template #fallback v-if="!widgetType">
      <SkeletonPage class="pt-[102px] max-w-[850px] px-[70px]" />
    </template>
    <div style="height: 100%">
      <component
        v-if="widgetType"
        :is="widget[widgetType].component"
        v-bind="widget[widgetType].propsData"
        v-on="widget[widgetType].eventHandlers"
      ></component>
      <Member
        v-else-if="eventType && eventType.includes('usercenter')"
        :onChange="onChange"
        :eventType="eventType"
        :userInfo="userInfo"
        :userBaseInfo="userBaseInfo"
      />
      <Container
        :data="data"
        :onChange="onChange"
        :onReady="onReady"
        :generateStatus="generateStatus"
        :eventType="eventType"
        :userInfo="userInfo"
        :userBaseInfo="userBaseInfo"
        :leftSmallStatus="leftSmallStatus"
        :pageParams="pageParams"
        v-else
      />
    </div>
  </suspense>

  <Toaster
    position="top-center"
    :toastOptions="{
      duration: 2000,
      unstyled: true,
      classes: {
        toast:
          'bg-black2 shadow-1 rounded-lg text-white text-sm px-4 min-h-[52px] py-1 flex items-center gap-3 fixed left-1/2 transform -translate-x-1/2 whitespace-nowrap toast-container',
        title: 'toast-title',
      },
    }"
  >
    <template #success-icon>
      <svg-icon name="Check" class="w-5 h-5" />
    </template>
  </Toaster>
</template>
<style></style>
