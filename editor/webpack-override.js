/**
 * 固定回调，在这里重写webpack的配置
 * @param {*} config webpack的配置,可在此基础上进行修改
 * @param {*} env 环境变了，development｜production
 * @return {*} 必须返回修改后的config
 */

const { publicPath } = require('./package.json');

module.exports = function (config, env) {
  if (env === 'development') {
    config.devServer.proxy.push({
      context: ['/api'],
      target: 'http://localhost:30000',
      secure: false,

      // pathRewrite: { '^/api': '' },
    });
  }
  config.resolve.alias['@tiptap'] = '@';
  if (publicPath !== './') {
    config.plugins[1]._options.exposes['./app'] = './src/app.vue';
    config.plugins[1]._options.exposes['./svg-icon'] = './src/components/svg-icon/index.vue';
    config.plugins[1]._options.exposes['./vue'] =
      './node_modules/vue/dist/vue.runtime.esm-bundler.js';
  }
  config.resolve.alias.vue$ = 'vue/dist/vue.esm-bundler.js';
  return config;
};
